<%- include('partials/header') %>

<!-- 500 Error Page -->
<section class="min-h-screen bg-gray-900 flex items-center justify-center py-12">
    <div class="max-w-md mx-auto text-center px-4">
        <!-- Error Icon -->
        <div class="mb-8">
            <i class="fas fa-server text-6xl text-red-400"></i>
        </div>
        
        <!-- Error Code -->
        <h1 class="text-6xl font-bold text-white mb-4">500</h1>
        
        <!-- Error Message -->
        <h2 class="text-2xl font-semibold text-gray-300 mb-4">Internal Server Error</h2>
        <p class="text-gray-400 mb-8">
            Something went wrong on our end. We're working to fix this issue. Please try again later.
        </p>
        
        <!-- Action Buttons -->
        <div class="space-y-4">
            <button onclick="location.reload()" class="block w-full bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                <i class="fas fa-redo mr-2"></i>
                Try Again
            </button>
            <a href="/" class="block bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                <i class="fas fa-home mr-2"></i>
                Go Home
            </a>
            <button onclick="history.back()" class="block w-full bg-gray-600 hover:bg-gray-500 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Go Back
            </button>
        </div>
        
        <!-- Error Details (for development) -->
        <div class="mt-8 pt-8 border-t border-gray-700">
            <details class="text-left">
                <summary class="text-gray-400 text-sm cursor-pointer hover:text-gray-300">Technical Details</summary>
                <div class="mt-4 bg-gray-800 rounded-lg p-4">
                    <p class="text-gray-300 text-xs font-mono">
                        Error ID: <%= Date.now() %><br>
                        Timestamp: <%= new Date().toISOString() %><br>
                        User Agent: <span id="user-agent"></span>
                    </p>
                </div>
            </details>
        </div>
        
        <!-- Contact Info -->
        <div class="mt-8">
            <p class="text-gray-400 text-sm">
                If this problem persists, please contact support with the error ID above.
            </p>
        </div>
    </div>
</section>

<script>
// Add user agent info
document.getElementById('user-agent').textContent = navigator.userAgent;

// Auto-retry after 30 seconds
setTimeout(() => {
    if (confirm('Would you like to try reloading the page?')) {
        location.reload();
    }
}, 30000);
</script>

<%- include('partials/footer') %>
