# 🛡️ Penetration Testing Learning Platform

A comprehensive, interactive web-based learning platform for penetration testing education, covering everything from basic concepts to advanced techniques.

## 🌟 Features

### 📚 **35 Interactive Learning Slides**
- **Basic Level**: Introduction, methodology, reconnaissance, scanning
- **Intermediate Level**: Web application testing, vulnerability assessment, network penetration
- **Advanced Level**: Red team operations, evasion techniques, cloud security, AI/ML security

### 🛠️ **Comprehensive Tools Database**
- **Reconnaissance**: Nmap, Maltego, theHarvester, Gobuster
- **Web Application**: Burp Suite, OWASP ZAP, Nikto, SQLmap
- **Exploitation**: Metasploit, custom exploits, payload generation
- **Password Cracking**: <PERSON>, <PERSON>h<PERSON>, Hydra
- **Network Analysis**: Wireshark, Responder, network tools
- **Wireless**: Aircrack-ng, wireless security testing

### 📊 **HTTP Status Codes Reference**
- Complete status code database (1xx-5xx)
- Penetration testing context for each code
- Security implications and testing scenarios
- Common attack vectors and detection methods

### 💻 **Interactive Code Examples**
- **Bash Scripts**: Network reconnaissance, automation
- **Python Scripts**: Web scraping, directory brute forcing, custom tools
- **PowerShell**: Active Directory enumeration, Windows testing
- **SQL Payloads**: Injection testing, database exploitation
- **One-liner Commands**: Quick reference for common tasks

### 🎯 **Advanced Topics**
- **Red Team Operations**: C2 frameworks, adversary simulation
- **Evasion Techniques**: AV bypass, network detection evasion
- **Cloud Security**: AWS/Azure/GCP testing, container security
- **AI/ML Security**: Adversarial examples, model testing
- **Real-world Case Studies**: Enterprise, cloud, mobile scenarios

### 🎨 **Modern UI/UX**
- **Dark/Light Theme**: Automatic and manual theme switching
- **Responsive Design**: Mobile-first, works on all devices
- **Progressive Web App**: Offline support, caching
- **Smooth Animations**: Enhanced user experience
- **Search Functionality**: Global search across all content

## 🚀 Quick Start

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Free_trainning
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm start
   ```

4. **Open your browser**
   ```
   http://localhost:3000
   ```

## 📁 Project Structure

```
Free_trainning/
├── 📁 data/                    # JSON data files
│   ├── slides.json            # 35 learning slides
│   ├── tools.json             # Tools database
│   ├── status-codes.json      # HTTP status codes
│   └── pentest-data.json      # Methodology & examples
├── 📁 public/                 # Static assets
│   ├── 📁 css/               # Stylesheets
│   ├── 📁 js/                # Client-side JavaScript
│   ├── 📁 images/            # Images and icons
│   └── sw.js                 # Service worker
├── 📁 views/                  # EJS templates
│   ├── 📁 partials/          # Reusable components
│   ├── index.ejs             # Homepage
│   ├── slides.ejs            # Slides overview
│   ├── slide-detail.ejs      # Individual slide
│   ├── methodology.ejs       # Testing methodology
│   ├── tools.ejs             # Tools database
│   ├── status-codes.ejs      # Status codes reference
│   ├── examples.ejs          # Code examples
│   ├── advanced.ejs          # Advanced topics
│   ├── 404.ejs               # Error pages
│   └── 500.ejs
├── server.js                  # Express server
├── package.json              # Dependencies
└── README.md                 # This file
```

## 🎓 Learning Path

### 1. **Fundamentals** (Slides 1-10)
- Introduction to penetration testing
- Legal and ethical considerations
- Basic methodology (PTES, OWASP)
- Information gathering techniques
- Network scanning fundamentals

### 2. **Intermediate Skills** (Slides 11-25)
- Web application security testing
- OWASP Top 10 vulnerabilities
- Network penetration techniques
- Vulnerability assessment
- Social engineering basics

### 3. **Advanced Techniques** (Slides 26-35)
- Red team operations
- Advanced evasion techniques
- Cloud security testing
- Mobile application security
- AI/ML security testing
- Emerging threats

## 🛠️ Key Technologies

- **Backend**: Node.js, Express.js
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Styling**: Tailwind CSS, Custom CSS
- **Templating**: EJS
- **Icons**: Font Awesome
- **Code Highlighting**: Prism.js
- **Interactivity**: Alpine.js
- **PWA**: Service Workers, Offline Support

## 📱 Features in Detail

### **Progressive Web App (PWA)**
- Offline functionality with service worker
- Caching for improved performance
- Mobile app-like experience
- Background sync capabilities

### **Theme System**
- Automatic dark/light mode detection
- Manual theme switching
- Persistent theme preferences
- High contrast mode support

### **Progress Tracking**
- Local storage for progress
- Slide completion tracking
- Bookmark functionality
- Resume where you left off

### **Search & Navigation**
- Global search across all content
- Keyboard navigation support
- Breadcrumb navigation
- Quick access shortcuts

## 🔒 Security Features

### **Educational Focus**
- All examples are for educational purposes
- Clear legal disclaimers
- Ethical hacking emphasis
- Responsible disclosure guidelines

### **Content Security**
- CSP headers implemented
- XSS protection
- Input validation
- Secure headers

## 📚 Content Highlights

### **Comprehensive Coverage**
- **35 detailed slides** covering all aspects
- **15+ professional tools** with examples
- **25+ HTTP status codes** with context
- **50+ code examples** ready to use
- **Real-world scenarios** and case studies

### **Industry Standards**
- OWASP methodology
- NIST guidelines
- PTES framework
- MITRE ATT&CK mapping

## 🎯 Target Audience

- **Cybersecurity Students**: Learning penetration testing fundamentals
- **IT Professionals**: Expanding security knowledge
- **Ethical Hackers**: Reference material and techniques
- **Security Researchers**: Advanced topics and methodologies
- **Bug Bounty Hunters**: Tools and techniques reference

## ⚖️ Legal Notice

**IMPORTANT**: This platform is designed for educational purposes only. All penetration testing techniques and tools should only be used on systems you own or have explicit permission to test. Unauthorized access to computer systems is illegal and may result in criminal charges.

## 🤝 Contributing

Contributions are welcome! Please read our contributing guidelines and submit pull requests for any improvements.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- OWASP for security testing guidelines
- Security community for tools and techniques
- Open source contributors
- Educational institutions promoting ethical hacking

---

**🚀 Start your penetration testing journey today!**

Visit: `http://localhost:3000` after starting the server.
