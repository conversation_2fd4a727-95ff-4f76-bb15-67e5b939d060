<%- include('partials/header') %>

<!-- <PERSON><PERSON> Header -->
<section class="bg-gradient-to-r from-gray-900 via-blue-900 to-gray-900 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl font-bold text-white mb-4">
                <i class="fas fa-tools mr-3"></i>
                Penetration Testing Tools
            </h1>
            <p class="text-xl text-gray-300 mb-6">
                Comprehensive database of essential tools used by security professionals
            </p>
            <div class="flex justify-center space-x-4 flex-wrap">
                <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm mb-2">
                    <i class="fas fa-search mr-1"></i>Reconnaissance
                </span>
                <span class="bg-green-600 text-white px-3 py-1 rounded-full text-sm mb-2">
                    <i class="fas fa-globe mr-1"></i>Web Application
                </span>
                <span class="bg-red-600 text-white px-3 py-1 rounded-full text-sm mb-2">
                    <i class="fas fa-bomb mr-1"></i>Exploitation
                </span>
                <span class="bg-purple-600 text-white px-3 py-1 rounded-full text-sm mb-2">
                    <i class="fas fa-network-wired mr-1"></i>Network Analysis
                </span>
                <span class="bg-yellow-600 text-white px-3 py-1 rounded-full text-sm mb-2">
                    <i class="fas fa-key mr-1"></i>Password Cracking
                </span>
            </div>
        </div>
    </div>
</section>

<!-- Filter Section -->
<div class="bg-gray-800 py-4">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div class="flex space-x-2 flex-wrap">
                <button onclick="filterTools('all')" class="filter-btn active bg-gray-600 hover:bg-gray-500 px-3 py-1 rounded text-white text-sm">All Tools</button>
                <button onclick="filterTools('reconnaissance')" class="filter-btn bg-blue-600 hover:bg-blue-500 px-3 py-1 rounded text-white text-sm">Reconnaissance</button>
                <button onclick="filterTools('web-application')" class="filter-btn bg-green-600 hover:bg-green-500 px-3 py-1 rounded text-white text-sm">Web App</button>
                <button onclick="filterTools('exploitation')" class="filter-btn bg-red-600 hover:bg-red-500 px-3 py-1 rounded text-white text-sm">Exploitation</button>
                <button onclick="filterTools('network-analysis')" class="filter-btn bg-purple-600 hover:bg-purple-500 px-3 py-1 rounded text-white text-sm">Network</button>
                <button onclick="filterTools('password-cracking')" class="filter-btn bg-yellow-600 hover:bg-yellow-500 px-3 py-1 rounded text-white text-sm">Password</button>
                <button onclick="filterTools('vulnerability-scanning')" class="filter-btn bg-indigo-600 hover:bg-indigo-500 px-3 py-1 rounded text-white text-sm">Scanning</button>
                <button onclick="filterTools('wireless')" class="filter-btn bg-pink-600 hover:bg-pink-500 px-3 py-1 rounded text-white text-sm">Wireless</button>
            </div>
            <div class="relative">
                <input type="text" id="tool-search" placeholder="Search tools..." 
                       class="bg-gray-700 text-white placeholder-gray-400 rounded-lg px-4 py-2 pl-10 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
            </div>
        </div>
    </div>
</div>

<!-- Tools Grid -->
<section class="py-12 bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="tools-container">
            <% tools.forEach(tool => { %>
            <div class="tool-card bg-gray-800 rounded-lg overflow-hidden hover:bg-gray-700 transition-all transform hover:scale-105" 
                 data-category="<%= tool.category %>" data-name="<%= tool.name.toLowerCase() %>">
                <!-- Tool Header -->
                <div class="p-6 border-b border-gray-700">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-xl font-semibold text-white"><%= tool.name %></h3>
                        <div class="category-badge">
                            <% if (tool.category === 'reconnaissance') { %>
                                <span class="bg-blue-600 text-white text-xs px-2 py-1 rounded-full">Recon</span>
                            <% } else if (tool.category === 'web-application') { %>
                                <span class="bg-green-600 text-white text-xs px-2 py-1 rounded-full">Web</span>
                            <% } else if (tool.category === 'exploitation') { %>
                                <span class="bg-red-600 text-white text-xs px-2 py-1 rounded-full">Exploit</span>
                            <% } else if (tool.category === 'network-analysis') { %>
                                <span class="bg-purple-600 text-white text-xs px-2 py-1 rounded-full">Network</span>
                            <% } else if (tool.category === 'password-cracking') { %>
                                <span class="bg-yellow-600 text-white text-xs px-2 py-1 rounded-full">Password</span>
                            <% } else if (tool.category === 'vulnerability-scanning') { %>
                                <span class="bg-indigo-600 text-white text-xs px-2 py-1 rounded-full">Scanning</span>
                            <% } else if (tool.category === 'wireless') { %>
                                <span class="bg-pink-600 text-white text-xs px-2 py-1 rounded-full">Wireless</span>
                            <% } else { %>
                                <span class="bg-gray-600 text-white text-xs px-2 py-1 rounded-full"><%= tool.category %></span>
                            <% } %>
                        </div>
                    </div>
                    <p class="text-gray-300 text-sm"><%= tool.description %></p>
                </div>
                
                <!-- Tool Content -->
                <div class="p-6">
                    <!-- Usage -->
                    <div class="mb-4">
                        <h4 class="text-sm font-semibold text-gray-400 mb-2">Basic Usage:</h4>
                        <code class="bg-gray-900 text-green-400 px-2 py-1 rounded text-xs block"><%= tool.usage %></code>
                    </div>
                    
                    <!-- Common Commands -->
                    <% if (tool.common_commands && tool.common_commands.length > 0) { %>
                    <div class="mb-4">
                        <h4 class="text-sm font-semibold text-gray-400 mb-2">Common Commands:</h4>
                        <div class="space-y-1">
                            <% tool.common_commands.slice(0, 3).forEach(command => { %>
                            <code class="bg-gray-900 text-blue-400 px-2 py-1 rounded text-xs block"><%= command %></code>
                            <% }); %>
                            <% if (tool.common_commands.length > 3) { %>
                            <button onclick="toggleCommands(this)" class="text-blue-400 text-xs hover:text-blue-300">
                                Show <%= tool.common_commands.length - 3 %> more commands...
                            </button>
                            <div class="hidden additional-commands space-y-1 mt-1">
                                <% tool.common_commands.slice(3).forEach(command => { %>
                                <code class="bg-gray-900 text-blue-400 px-2 py-1 rounded text-xs block"><%= command %></code>
                                <% }); %>
                            </div>
                            <% } %>
                        </div>
                    </div>
                    <% } %>
                    
                    <!-- Pros and Cons -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <% if (tool.pros && tool.pros.length > 0) { %>
                        <div>
                            <h4 class="text-sm font-semibold text-green-400 mb-2">Pros:</h4>
                            <ul class="text-xs text-gray-300 space-y-1">
                                <% tool.pros.slice(0, 2).forEach(pro => { %>
                                <li class="flex items-center">
                                    <i class="fas fa-check text-green-400 mr-1"></i>
                                    <%= pro %>
                                </li>
                                <% }); %>
                            </ul>
                        </div>
                        <% } %>
                        
                        <% if (tool.cons && tool.cons.length > 0) { %>
                        <div>
                            <h4 class="text-sm font-semibold text-red-400 mb-2">Cons:</h4>
                            <ul class="text-xs text-gray-300 space-y-1">
                                <% tool.cons.slice(0, 2).forEach(con => { %>
                                <li class="flex items-center">
                                    <i class="fas fa-times text-red-400 mr-1"></i>
                                    <%= con %>
                                </li>
                                <% }); %>
                            </ul>
                        </div>
                        <% } %>
                    </div>
                    
                    <!-- Installation -->
                    <div class="mb-4">
                        <h4 class="text-sm font-semibold text-gray-400 mb-2">Installation:</h4>
                        <p class="text-xs text-gray-300"><%= tool.installation %></p>
                    </div>
                    
                    <!-- Alternatives -->
                    <% if (tool.alternatives && tool.alternatives.length > 0) { %>
                    <div class="mb-4">
                        <h4 class="text-sm font-semibold text-gray-400 mb-2">Alternatives:</h4>
                        <div class="flex flex-wrap gap-1">
                            <% tool.alternatives.forEach(alt => { %>
                            <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded"><%= alt %></span>
                            <% }); %>
                        </div>
                    </div>
                    <% } %>
                    
                    <!-- Action Buttons -->
                    <div class="flex space-x-2">
                        <button onclick="copyInstallCommand('<%= tool.installation %>')" 
                                class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-lg font-semibold transition-colors text-sm">
                            <i class="fas fa-download mr-1"></i>
                            Install
                        </button>
                        <button onclick="bookmarkTool('<%= tool.name %>')" 
                                class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-2 rounded-lg transition-colors">
                            <i class="fas fa-bookmark"></i>
                        </button>
                    </div>
                </div>
            </div>
            <% }); %>
        </div>
        
        <!-- No Results Message -->
        <div id="no-results" class="text-center py-12 hidden">
            <i class="fas fa-search text-4xl text-gray-600 mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-400 mb-2">No tools found</h3>
            <p class="text-gray-500">Try adjusting your filter or search criteria</p>
        </div>
    </div>
</section>

<!-- Quick Reference Section -->
<section class="py-8 bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-white mb-4">Quick Reference</h2>
            <p class="text-gray-400">Essential commands and techniques</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div class="bg-gray-700 rounded-lg p-4">
                <h3 class="text-lg font-semibold text-white mb-2">Network Scanning</h3>
                <code class="text-green-400 text-sm">nmap -sS -O target_ip</code>
            </div>
            <div class="bg-gray-700 rounded-lg p-4">
                <h3 class="text-lg font-semibold text-white mb-2">Web Testing</h3>
                <code class="text-green-400 text-sm">burpsuite --config-file=config.json</code>
            </div>
            <div class="bg-gray-700 rounded-lg p-4">
                <h3 class="text-lg font-semibold text-white mb-2">SQL Injection</h3>
                <code class="text-green-400 text-sm">sqlmap -u "url" --dbs</code>
            </div>
        </div>
    </div>
</section>

<script>
// Tools filtering and search functionality
function filterTools(category) {
    const tools = document.querySelectorAll('.tool-card');
    const filterBtns = document.querySelectorAll('.filter-btn');
    const noResults = document.getElementById('no-results');
    
    // Update active filter button
    filterBtns.forEach(btn => btn.classList.remove('active', 'bg-gray-600'));
    event.target.classList.add('active', 'bg-gray-600');
    
    let visibleCount = 0;
    tools.forEach(tool => {
        if (category === 'all' || tool.dataset.category === category) {
            tool.style.display = 'block';
            visibleCount++;
        } else {
            tool.style.display = 'none';
        }
    });
    
    noResults.style.display = visibleCount === 0 ? 'block' : 'none';
}

function toggleCommands(button) {
    const additionalCommands = button.nextElementSibling;
    if (additionalCommands.classList.contains('hidden')) {
        additionalCommands.classList.remove('hidden');
        button.textContent = 'Show less...';
    } else {
        additionalCommands.classList.add('hidden');
        button.textContent = button.textContent.replace('Show less...', 'Show more commands...');
    }
}

function copyInstallCommand(command) {
    navigator.clipboard.writeText(command).then(() => {
        window.notificationManager?.show('Installation command copied!', 'success');
    }).catch(() => {
        window.notificationManager?.show('Failed to copy command', 'error');
    });
}

function bookmarkTool(toolName) {
    let bookmarks = JSON.parse(localStorage.getItem('bookmarkedTools') || '[]');
    if (!bookmarks.includes(toolName)) {
        bookmarks.push(toolName);
        localStorage.setItem('bookmarkedTools', JSON.stringify(bookmarks));
        window.notificationManager?.show(`${toolName} bookmarked!`, 'success');
    } else {
        window.notificationManager?.show(`${toolName} already bookmarked`, 'info');
    }
}

// Search functionality
document.getElementById('tool-search')?.addEventListener('input', function(e) {
    const query = e.target.value.toLowerCase();
    const tools = document.querySelectorAll('.tool-card');
    const noResults = document.getElementById('no-results');
    
    let visibleCount = 0;
    tools.forEach(tool => {
        const name = tool.dataset.name;
        const description = tool.querySelector('p').textContent.toLowerCase();
        
        if (name.includes(query) || description.includes(query)) {
            tool.style.display = 'block';
            visibleCount++;
        } else {
            tool.style.display = 'none';
        }
    });
    
    noResults.style.display = visibleCount === 0 ? 'block' : 'none';
});
</script>

<%- include('partials/footer') %>
