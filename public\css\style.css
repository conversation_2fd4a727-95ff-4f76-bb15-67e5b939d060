/* Custom CSS for Penetration Testing Learning Platform */

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #1f2937;
}

::-webkit-scrollbar-thumb {
    background: #4b5563;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
}

/* Code Blocks */
pre[class*="language-"] {
    background: #1e293b !important;
    border: 1px solid #334155;
    border-radius: 8px;
    margin: 1rem 0;
}

code[class*="language-"] {
    color: #e2e8f0 !important;
    font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
    font-size: 0.875rem;
}

/* Inline code */
:not(pre) > code {
    background: #374151;
    color: #f59e0b;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

/* Custom Components */
.gradient-text {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: rgba(31, 41, 55, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(75, 85, 99, 0.3);
}

.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
}

/* Status Indicators */
.status-success {
    color: #10b981;
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-warning {
    color: #f59e0b;
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.status-error {
    color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.status-info {
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
}

/* Progress Indicators */
.progress-ring {
    transform: rotate(-90deg);
}

.progress-ring-circle {
    transition: stroke-dashoffset 0.35s;
    transform-origin: 50% 50%;
}

/* Slide Navigation */
.slide-nav {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 40;
}

.slide-nav button {
    background: rgba(31, 41, 55, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(75, 85, 99, 0.3);
    color: white;
    padding: 0.75rem;
    border-radius: 50%;
    margin: 0.25rem;
    transition: all 0.3s ease;
}

.slide-nav button:hover {
    background: rgba(239, 68, 68, 0.9);
    transform: scale(1.1);
}

/* Search Results */
.search-highlight {
    background: rgba(245, 158, 11, 0.3);
    color: #fbbf24;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
}

/* Tool Cards */
.tool-card {
    background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    border: 1px solid #4b5563;
    transition: all 0.3s ease;
}

.tool-card:hover {
    border-color: #ef4444;
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.2);
}

/* Status Code Cards */
.status-code-card {
    border-left: 4px solid;
    transition: all 0.3s ease;
}

.status-code-card.informational {
    border-left-color: #3b82f6;
}

.status-code-card.success {
    border-left-color: #10b981;
}

.status-code-card.redirection {
    border-left-color: #f59e0b;
}

.status-code-card.client-error {
    border-left-color: #ef4444;
}

.status-code-card.server-error {
    border-left-color: #dc2626;
}

/* Methodology Timeline */
.timeline {
    position: relative;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #ef4444, #dc2626);
}

.timeline-item {
    position: relative;
    padding-left: 3rem;
    margin-bottom: 2rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: 0.5rem;
    top: 0.5rem;
    width: 1rem;
    height: 1rem;
    background: #ef4444;
    border-radius: 50%;
    border: 3px solid #1f2937;
}

/* Responsive Design */
@media (max-width: 768px) {
    .slide-nav {
        bottom: 1rem;
        right: 1rem;
    }
    
    .timeline::before {
        left: 0.5rem;
    }
    
    .timeline-item {
        padding-left: 2rem;
    }
    
    .timeline-item::before {
        left: 0.25rem;
        width: 0.5rem;
        height: 0.5rem;
    }
}

/* Dark Mode Toggle */
.dark-mode-toggle {
    transition: all 0.3s ease;
}

.dark-mode-toggle:hover {
    transform: rotate(180deg);
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
    
    .bg-gray-900,
    .bg-gray-800,
    .bg-gray-700 {
        background: white !important;
        color: black !important;
    }
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus States */
button:focus,
a:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid #ef4444;
    outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .bg-gray-900 { background-color: #000000 !important; }
    .bg-gray-800 { background-color: #1a1a1a !important; }
    .bg-gray-700 { background-color: #333333 !important; }
    .text-gray-300 { color: #ffffff !important; }
    .text-gray-400 { color: #cccccc !important; }
}

/* Enhanced Theme Styles */
:root {
    --primary-color: #ef4444;
    --primary-dark: #dc2626;
    --secondary-color: #3b82f6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-tertiary: #374151;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --border-color: #4b5563;
    --shadow-color: rgba(0, 0, 0, 0.3);
}

[data-theme="light"] {
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #e2e8f0;
    --text-primary: #1e293b;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --border-color: #cbd5e1;
    --shadow-color: rgba(0, 0, 0, 0.1);
}

/* Theme-aware components */
.theme-bg-primary { background-color: var(--bg-primary); }
.theme-bg-secondary { background-color: var(--bg-secondary); }
.theme-bg-tertiary { background-color: var(--bg-tertiary); }
.theme-text-primary { color: var(--text-primary); }
.theme-text-secondary { color: var(--text-secondary); }
.theme-text-muted { color: var(--text-muted); }
.theme-border { border-color: var(--border-color); }

/* Enhanced animations */
@keyframes slideInFromLeft {
    0% { transform: translateX(-100%); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

@keyframes slideInFromRight {
    0% { transform: translateX(100%); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

@keyframes slideInFromTop {
    0% { transform: translateY(-100%); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
}

@keyframes slideInFromBottom {
    0% { transform: translateY(100%); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
}

@keyframes scaleIn {
    0% { transform: scale(0.8); opacity: 0; }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 5px var(--primary-color); }
    50% { box-shadow: 0 0 20px var(--primary-color), 0 0 30px var(--primary-color); }
}

/* Animation classes */
.animate-slide-in-left { animation: slideInFromLeft 0.6s ease-out; }
.animate-slide-in-right { animation: slideInFromRight 0.6s ease-out; }
.animate-slide-in-top { animation: slideInFromTop 0.6s ease-out; }
.animate-slide-in-bottom { animation: slideInFromBottom 0.6s ease-out; }
.animate-scale-in { animation: scaleIn 0.4s ease-out; }
.animate-bounce-in { animation: bounceIn 0.6s ease-out; }
.animate-glow { animation: glow 2s infinite; }

/* Enhanced hover effects */
.hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px -12px var(--shadow-color);
}

.hover-glow {
    transition: all 0.3s ease;
    position: relative;
}

.hover-glow:hover {
    box-shadow: 0 0 20px var(--primary-color);
}

.hover-glow::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(239, 68, 68, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.hover-glow:hover::before {
    opacity: 1;
}

/* Enhanced buttons */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(239, 68, 68, 0.3);
}

/* Enhanced cards */
.card-enhanced {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    padding: 1.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--success-color));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.card-enhanced:hover::before {
    transform: scaleX(1);
}

.card-enhanced:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px var(--shadow-color);
    border-color: var(--primary-color);
}

/* Enhanced navigation */
.nav-enhanced {
    backdrop-filter: blur(20px);
    background: rgba(31, 41, 55, 0.9);
    border-bottom: 1px solid rgba(75, 85, 99, 0.3);
}

.nav-item {
    position: relative;
    transition: all 0.3s ease;
}

.nav-item::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-item:hover::after,
.nav-item.active::after {
    width: 100%;
}

/* Enhanced progress indicators */
.progress-enhanced {
    background: var(--bg-tertiary);
    border-radius: 1rem;
    overflow: hidden;
    position: relative;
}

.progress-bar-enhanced {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    height: 100%;
    border-radius: 1rem;
    position: relative;
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-bar-enhanced::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Enhanced tooltips */
.tooltip-enhanced {
    position: relative;
    cursor: help;
}

.tooltip-enhanced::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-primary);
    color: var(--text-primary);
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    border: 1px solid var(--border-color);
    box-shadow: 0 10px 25px var(--shadow-color);
}

.tooltip-enhanced::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--bg-primary);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.tooltip-enhanced:hover::before,
.tooltip-enhanced:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}

/* Responsive enhancements */
@media (max-width: 768px) {
    .card-enhanced {
        padding: 1rem;
    }

    .btn-primary {
        padding: 0.625rem 1.25rem;
        font-size: 0.875rem;
    }

    .animate-slide-in-left,
    .animate-slide-in-right {
        animation: fadeIn 0.6s ease-out;
    }
}

/* Dark mode specific enhancements */
@media (prefers-color-scheme: dark) {
    .nav-enhanced {
        background: rgba(17, 24, 39, 0.9);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
