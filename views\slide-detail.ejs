<%- include('partials/header') %>

<!-- Slide Header -->
<section class="bg-gradient-to-r from-gray-900 via-red-900 to-gray-900 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <% if (prevSlide) { %>
                <a href="/slides/<%= prevSlide %>" class="bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-lg transition-colors">
                    <i class="fas fa-chevron-left"></i>
                </a>
                <% } %>
                
                <div>
                    <div class="flex items-center space-x-2 mb-1">
                        <span class="text-gray-400 text-sm">Slide <%= slide.id %> of 35</span>
                        <% if (slide.category === 'basics') { %>
                            <span class="bg-green-600 text-white text-xs px-2 py-1 rounded-full">Basic</span>
                        <% } else if (slide.category === 'intermediate') { %>
                            <span class="bg-yellow-600 text-white text-xs px-2 py-1 rounded-full">Intermediate</span>
                        <% } else { %>
                            <span class="bg-red-600 text-white text-xs px-2 py-1 rounded-full">Advanced</span>
                        <% } %>
                    </div>
                    <h1 class="text-3xl font-bold text-white"><%= slide.title %></h1>
                </div>
                
                <% if (nextSlide) { %>
                <a href="/slides/<%= nextSlide %>" class="bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-lg transition-colors">
                    <i class="fas fa-chevron-right"></i>
                </a>
                <% } %>
            </div>
            
            <div class="flex items-center space-x-2">
                <button onclick="toggleCompletion(<%= slide.id %>)" 
                        class="completion-btn bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-check mr-1"></i>
                    <span class="completion-text">Mark Complete</span>
                </button>
                <a href="/slides" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-th mr-1"></i>
                    All Slides
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Progress Bar -->
<div class="bg-gray-800 py-2">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-gray-700 rounded-full h-2">
            <div class="bg-red-600 h-2 rounded-full transition-all duration-300" style="width: <%= (slide.id / 35) * 100 %>%"></div>
        </div>
    </div>
</div>

<!-- Main Content -->
<section class="py-12 bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Slide Content -->
            <div class="lg:col-span-2">
                <!-- Slide Visual -->
                <div class="bg-gradient-to-br from-gray-800 to-gray-700 rounded-lg p-8 mb-8 min-h-96 flex items-center justify-center">
                    <div class="text-center">
                        <div class="text-6xl text-red-400 mb-6">
                            <% if (slide.category === 'basics') { %>
                                <i class="fas fa-play-circle"></i>
                            <% } else if (slide.category === 'intermediate') { %>
                                <i class="fas fa-cogs"></i>
                            <% } else { %>
                                <i class="fas fa-rocket"></i>
                            <% } %>
                        </div>
                        <h2 class="text-2xl font-bold text-white mb-4"><%= slide.title %></h2>
                        <p class="text-gray-300 text-lg max-w-2xl"><%= slide.content %></p>
                    </div>
                </div>
                
                <!-- Key Points -->
                <% if (slide.keyPoints && slide.keyPoints.length > 0) { %>
                <div class="bg-gray-800 rounded-lg p-6 mb-8">
                    <h3 class="text-xl font-semibold text-white mb-4">
                        <i class="fas fa-list-ul text-red-400 mr-2"></i>
                        Key Learning Points
                    </h3>
                    <ul class="space-y-3">
                        <% slide.keyPoints.forEach(point => { %>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-400 mr-3 mt-1 flex-shrink-0"></i>
                            <span class="text-gray-300"><%= point %></span>
                        </li>
                        <% }); %>
                    </ul>
                </div>
                <% } %>
                
                <!-- Code Example -->
                <% if (slide.codeExample) { %>
                <div class="bg-gray-800 rounded-lg p-6 mb-8">
                    <h3 class="text-xl font-semibold text-white mb-4">
                        <i class="fas fa-code text-red-400 mr-2"></i>
                        Code Example
                    </h3>
                    <div class="relative">
                        <pre class="language-bash"><code><%= slide.codeExample %></code></pre>
                    </div>
                </div>
                <% } %>
                
                <!-- Navigation -->
                <div class="flex justify-between items-center">
                    <% if (prevSlide) { %>
                    <a href="/slides/<%= prevSlide %>" 
                       class="bg-gray-700 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors inline-flex items-center">
                        <i class="fas fa-chevron-left mr-2"></i>
                        Previous Slide
                    </a>
                    <% } else { %>
                    <div></div>
                    <% } %>
                    
                    <% if (nextSlide) { %>
                    <a href="/slides/<%= nextSlide %>" 
                       class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors inline-flex items-center">
                        Next Slide
                        <i class="fas fa-chevron-right ml-2"></i>
                    </a>
                    <% } else { %>
                    <a href="/methodology" 
                       class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors inline-flex items-center">
                        Continue to Methodology
                        <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                    <% } %>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Slide Info -->
                <div class="bg-gray-800 rounded-lg p-6 mb-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Slide Information</h3>
                    <div class="space-y-3 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Category:</span>
                            <span class="text-white capitalize"><%= slide.category %></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Slide Number:</span>
                            <span class="text-white"><%= slide.id %> of 35</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Difficulty:</span>
                            <span class="text-white">
                                <% if (slide.category === 'basics') { %>
                                    <i class="fas fa-star text-green-400"></i>
                                    <i class="fas fa-star text-gray-600"></i>
                                    <i class="fas fa-star text-gray-600"></i>
                                <% } else if (slide.category === 'intermediate') { %>
                                    <i class="fas fa-star text-yellow-400"></i>
                                    <i class="fas fa-star text-yellow-400"></i>
                                    <i class="fas fa-star text-gray-600"></i>
                                <% } else { %>
                                    <i class="fas fa-star text-red-400"></i>
                                    <i class="fas fa-star text-red-400"></i>
                                    <i class="fas fa-star text-red-400"></i>
                                <% } %>
                            </span>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="bg-gray-800 rounded-lg p-6 mb-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Quick Actions</h3>
                    <div class="space-y-2">
                        <button onclick="printSlide()" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors">
                            <i class="fas fa-print mr-2"></i>
                            Print Slide
                        </button>
                        <button onclick="shareSlide()" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors">
                            <i class="fas fa-share mr-2"></i>
                            Share Slide
                        </button>
                        <button onclick="bookmarkSlide()" class="w-full bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded-lg transition-colors">
                            <i class="fas fa-bookmark mr-2"></i>
                            Bookmark
                        </button>
                    </div>
                </div>
                
                <!-- Related Topics -->
                <div class="bg-gray-800 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Related Topics</h3>
                    <div class="space-y-2">
                        <a href="/methodology" class="block text-blue-400 hover:text-blue-300 text-sm">
                            <i class="fas fa-arrow-right mr-2"></i>
                            Penetration Testing Methodology
                        </a>
                        <a href="/tools" class="block text-blue-400 hover:text-blue-300 text-sm">
                            <i class="fas fa-arrow-right mr-2"></i>
                            Related Tools & Techniques
                        </a>
                        <a href="/examples" class="block text-blue-400 hover:text-blue-300 text-sm">
                            <i class="fas fa-arrow-right mr-2"></i>
                            Code Examples
                        </a>
                        <a href="/status-codes" class="block text-blue-400 hover:text-blue-300 text-sm">
                            <i class="fas fa-arrow-right mr-2"></i>
                            HTTP Status Codes
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Slide-specific JavaScript
let currentSlideId = <%= slide.id %>;
let completedSlides = JSON.parse(localStorage.getItem('completedSlides') || '[]');

function toggleCompletion(slideId) {
    const index = completedSlides.indexOf(slideId);
    const btn = document.querySelector('.completion-btn');
    const text = btn.querySelector('.completion-text');
    
    if (index > -1) {
        completedSlides.splice(index, 1);
        btn.classList.remove('bg-green-600');
        btn.classList.add('bg-gray-600');
        text.textContent = 'Mark Complete';
        window.notificationManager?.show('Slide marked as incomplete', 'info');
    } else {
        completedSlides.push(slideId);
        btn.classList.remove('bg-gray-600');
        btn.classList.add('bg-green-600');
        text.textContent = 'Completed';
        window.notificationManager?.show('Slide completed!', 'success');
    }
    
    localStorage.setItem('completedSlides', JSON.stringify(completedSlides));
}

function printSlide() {
    window.print();
}

function shareSlide() {
    if (navigator.share) {
        navigator.share({
            title: '<%= slide.title %>',
            text: '<%= slide.content %>',
            url: window.location.href
        });
    } else {
        navigator.clipboard.writeText(window.location.href);
        window.notificationManager?.show('Link copied to clipboard!', 'success');
    }
}

function bookmarkSlide() {
    let bookmarks = JSON.parse(localStorage.getItem('bookmarkedSlides') || '[]');
    if (!bookmarks.includes(currentSlideId)) {
        bookmarks.push(currentSlideId);
        localStorage.setItem('bookmarkedSlides', JSON.stringify(bookmarks));
        window.notificationManager?.show('Slide bookmarked!', 'success');
    } else {
        window.notificationManager?.show('Slide already bookmarked', 'info');
    }
}

// Initialize completion status
document.addEventListener('DOMContentLoaded', function() {
    if (completedSlides.includes(currentSlideId)) {
        const btn = document.querySelector('.completion-btn');
        const text = btn.querySelector('.completion-text');
        btn.classList.remove('bg-gray-600');
        btn.classList.add('bg-green-600');
        text.textContent = 'Completed';
    }
});

// Keyboard navigation
document.addEventListener('keydown', function(e) {
    if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;
    
    switch(e.key) {
        case 'ArrowLeft':
            <% if (prevSlide) { %>
            window.location.href = '/slides/<%= prevSlide %>';
            <% } %>
            break;
        case 'ArrowRight':
            <% if (nextSlide) { %>
            window.location.href = '/slides/<%= nextSlide %>';
            <% } %>
            break;
        case ' ':
            e.preventDefault();
            toggleCompletion(currentSlideId);
            break;
    }
});
</script>

<%- include('partials/footer') %>
