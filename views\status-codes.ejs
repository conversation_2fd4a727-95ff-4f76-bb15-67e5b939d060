<%- include('partials/header') %>

<!-- Status Codes Header -->
<section class="bg-gradient-to-r from-gray-900 via-green-900 to-gray-900 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl font-bold text-white mb-4">
                <i class="fas fa-code mr-3"></i>
                HTTP Status Codes Reference
            </h1>
            <p class="text-xl text-gray-300 mb-6">
                Comprehensive guide to HTTP status codes with penetration testing context
            </p>
            <div class="flex justify-center space-x-4 flex-wrap">
                <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm mb-2">
                    <i class="fas fa-info-circle mr-1"></i>1xx Informational
                </span>
                <span class="bg-green-600 text-white px-3 py-1 rounded-full text-sm mb-2">
                    <i class="fas fa-check-circle mr-1"></i>2xx Success
                </span>
                <span class="bg-yellow-600 text-white px-3 py-1 rounded-full text-sm mb-2">
                    <i class="fas fa-arrow-right mr-1"></i>3xx Redirection
                </span>
                <span class="bg-red-600 text-white px-3 py-1 rounded-full text-sm mb-2">
                    <i class="fas fa-exclamation-triangle mr-1"></i>4xx Client Error
                </span>
                <span class="bg-purple-600 text-white px-3 py-1 rounded-full text-sm mb-2">
                    <i class="fas fa-server mr-1"></i>5xx Server Error
                </span>
            </div>
        </div>
    </div>
</section>

<!-- Filter Section -->
<div class="bg-gray-800 py-4">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div class="flex space-x-2 flex-wrap">
                <button onclick="filterCodes('all')" class="filter-btn active bg-gray-600 hover:bg-gray-500 px-3 py-1 rounded text-white text-sm">All Codes</button>
                <button onclick="filterCodes('Informational')" class="filter-btn bg-blue-600 hover:bg-blue-500 px-3 py-1 rounded text-white text-sm">1xx Info</button>
                <button onclick="filterCodes('Success')" class="filter-btn bg-green-600 hover:bg-green-500 px-3 py-1 rounded text-white text-sm">2xx Success</button>
                <button onclick="filterCodes('Redirection')" class="filter-btn bg-yellow-600 hover:bg-yellow-500 px-3 py-1 rounded text-white text-sm">3xx Redirect</button>
                <button onclick="filterCodes('Client Error')" class="filter-btn bg-red-600 hover:bg-red-500 px-3 py-1 rounded text-white text-sm">4xx Client</button>
                <button onclick="filterCodes('Server Error')" class="filter-btn bg-purple-600 hover:bg-purple-500 px-3 py-1 rounded text-white text-sm">5xx Server</button>
            </div>
            <div class="relative">
                <input type="text" id="code-search" placeholder="Search status codes..." 
                       class="bg-gray-700 text-white placeholder-gray-400 rounded-lg px-4 py-2 pl-10 focus:outline-none focus:ring-2 focus:ring-green-500">
                <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
            </div>
        </div>
    </div>
</div>

<!-- Status Codes Grid -->
<section class="py-12 bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="codes-container">
            <% statusCodes.forEach(statusCode => { %>
            <div class="status-code-card bg-gray-800 rounded-lg overflow-hidden hover:bg-gray-700 transition-all transform hover:scale-105 <%= statusCode.category.toLowerCase().replace(' ', '-') %>" 
                 data-category="<%= statusCode.category %>" data-code="<%= statusCode.code %>" data-name="<%= statusCode.name.toLowerCase() %>">
                <!-- Status Code Header -->
                <div class="p-6 border-b border-gray-700">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <div class="text-2xl font-bold 
                                <% if (statusCode.category === 'Informational') { %>text-blue-400
                                <% } else if (statusCode.category === 'Success') { %>text-green-400
                                <% } else if (statusCode.category === 'Redirection') { %>text-yellow-400
                                <% } else if (statusCode.category === 'Client Error') { %>text-red-400
                                <% } else { %>text-purple-400<% } %>">
                                <%= statusCode.code %>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-white"><%= statusCode.name %></h3>
                                <span class="text-xs text-gray-400"><%= statusCode.category %></span>
                            </div>
                        </div>
                        <div class="category-icon text-2xl 
                            <% if (statusCode.category === 'Informational') { %>text-blue-400
                            <% } else if (statusCode.category === 'Success') { %>text-green-400
                            <% } else if (statusCode.category === 'Redirection') { %>text-yellow-400
                            <% } else if (statusCode.category === 'Client Error') { %>text-red-400
                            <% } else { %>text-purple-400<% } %>">
                            <% if (statusCode.category === 'Informational') { %>
                                <i class="fas fa-info-circle"></i>
                            <% } else if (statusCode.category === 'Success') { %>
                                <i class="fas fa-check-circle"></i>
                            <% } else if (statusCode.category === 'Redirection') { %>
                                <i class="fas fa-arrow-right"></i>
                            <% } else if (statusCode.category === 'Client Error') { %>
                                <i class="fas fa-exclamation-triangle"></i>
                            <% } else { %>
                                <i class="fas fa-server"></i>
                            <% } %>
                        </div>
                    </div>
                    <p class="text-gray-300 text-sm"><%= statusCode.description %></p>
                </div>
                
                <!-- Status Code Content -->
                <div class="p-6">
                    <!-- Penetration Testing Context -->
                    <div class="mb-4">
                        <h4 class="text-sm font-semibold text-yellow-400 mb-2">
                            <i class="fas fa-shield-alt mr-1"></i>
                            Penetration Testing Context:
                        </h4>
                        <p class="text-gray-300 text-sm"><%= statusCode.pentesting_context %></p>
                    </div>
                    
                    <!-- Common Scenarios -->
                    <% if (statusCode.common_scenarios && statusCode.common_scenarios.length > 0) { %>
                    <div class="mb-4">
                        <h4 class="text-sm font-semibold text-blue-400 mb-2">
                            <i class="fas fa-list mr-1"></i>
                            Common Scenarios:
                        </h4>
                        <ul class="text-xs text-gray-300 space-y-1">
                            <% statusCode.common_scenarios.forEach(scenario => { %>
                            <li class="flex items-center">
                                <i class="fas fa-dot-circle text-blue-400 mr-2 text-xs"></i>
                                <%= scenario %>
                            </li>
                            <% }); %>
                        </ul>
                    </div>
                    <% } %>
                    
                    <!-- Security Implications -->
                    <div class="mb-4">
                        <h4 class="text-sm font-semibold text-red-400 mb-2">
                            <i class="fas fa-exclamation-circle mr-1"></i>
                            Security Implications:
                        </h4>
                        <p class="text-gray-300 text-sm"><%= statusCode.security_implications %></p>
                    </div>
                    
                    <!-- Testing Commands -->
                    <div class="mb-4">
                        <h4 class="text-sm font-semibold text-green-400 mb-2">
                            <i class="fas fa-terminal mr-1"></i>
                            Testing Commands:
                        </h4>
                        <div class="space-y-1">
                            <code class="bg-gray-900 text-green-400 px-2 py-1 rounded text-xs block">curl -I -s http://target.com | grep "HTTP/"</code>
                            <code class="bg-gray-900 text-green-400 px-2 py-1 rounded text-xs block">nmap --script http-enum target.com</code>
                            <% if (statusCode.code >= 400) { %>
                            <code class="bg-gray-900 text-green-400 px-2 py-1 rounded text-xs block">burpsuite --target http://target.com</code>
                            <% } %>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="flex space-x-2">
                        <button onclick="copyTestCommand('<%= statusCode.code %>')" 
                                class="flex-1 bg-green-600 hover:bg-green-700 text-white text-center py-2 px-4 rounded-lg font-semibold transition-colors text-sm">
                            <i class="fas fa-copy mr-1"></i>
                            Copy Test
                        </button>
                        <button onclick="bookmarkCode('<%= statusCode.code %>')" 
                                class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-2 rounded-lg transition-colors">
                            <i class="fas fa-bookmark"></i>
                        </button>
                        <button onclick="showExamples('<%= statusCode.code %>')" 
                                class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg transition-colors">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
            </div>
            <% }); %>
        </div>
        
        <!-- No Results Message -->
        <div id="no-results" class="text-center py-12 hidden">
            <i class="fas fa-search text-4xl text-gray-600 mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-400 mb-2">No status codes found</h3>
            <p class="text-gray-500">Try adjusting your filter or search criteria</p>
        </div>
    </div>
</section>

<!-- Quick Reference Chart -->
<section class="py-8 bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-white mb-4">Quick Reference Chart</h2>
            <p class="text-gray-400">Most common status codes in penetration testing</p>
        </div>
        
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div class="bg-green-600 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-white">200</div>
                <div class="text-sm text-green-100">OK</div>
            </div>
            <div class="bg-yellow-600 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-white">302</div>
                <div class="text-sm text-yellow-100">Found</div>
            </div>
            <div class="bg-red-600 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-white">401</div>
                <div class="text-sm text-red-100">Unauthorized</div>
            </div>
            <div class="bg-red-600 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-white">403</div>
                <div class="text-sm text-red-100">Forbidden</div>
            </div>
            <div class="bg-red-600 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-white">404</div>
                <div class="text-sm text-red-100">Not Found</div>
            </div>
            <div class="bg-purple-600 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-white">500</div>
                <div class="text-sm text-purple-100">Server Error</div>
            </div>
        </div>
    </div>
</section>

<script>
// Status codes filtering and search functionality
function filterCodes(category) {
    const codes = document.querySelectorAll('.status-code-card');
    const filterBtns = document.querySelectorAll('.filter-btn');
    const noResults = document.getElementById('no-results');
    
    // Update active filter button
    filterBtns.forEach(btn => btn.classList.remove('active', 'bg-gray-600'));
    event.target.classList.add('active', 'bg-gray-600');
    
    let visibleCount = 0;
    codes.forEach(code => {
        if (category === 'all' || code.dataset.category === category) {
            code.style.display = 'block';
            visibleCount++;
        } else {
            code.style.display = 'none';
        }
    });
    
    noResults.style.display = visibleCount === 0 ? 'block' : 'none';
}

function copyTestCommand(statusCode) {
    const command = `curl -I -s http://target.com | grep "${statusCode}"`;
    navigator.clipboard.writeText(command).then(() => {
        window.notificationManager?.show('Test command copied!', 'success');
    }).catch(() => {
        window.notificationManager?.show('Failed to copy command', 'error');
    });
}

function bookmarkCode(statusCode) {
    let bookmarks = JSON.parse(localStorage.getItem('bookmarkedCodes') || '[]');
    if (!bookmarks.includes(statusCode)) {
        bookmarks.push(statusCode);
        localStorage.setItem('bookmarkedCodes', JSON.stringify(bookmarks));
        window.notificationManager?.show(`Status code ${statusCode} bookmarked!`, 'success');
    } else {
        window.notificationManager?.show(`Status code ${statusCode} already bookmarked`, 'info');
    }
}

function showExamples(statusCode) {
    // This would open a modal or navigate to examples page
    window.notificationManager?.show(`Showing examples for status code ${statusCode}`, 'info');
}

// Search functionality
document.getElementById('code-search')?.addEventListener('input', function(e) {
    const query = e.target.value.toLowerCase();
    const codes = document.querySelectorAll('.status-code-card');
    const noResults = document.getElementById('no-results');
    
    let visibleCount = 0;
    codes.forEach(code => {
        const codeNum = code.dataset.code;
        const name = code.dataset.name;
        const description = code.querySelector('p').textContent.toLowerCase();
        
        if (codeNum.includes(query) || name.includes(query) || description.includes(query)) {
            code.style.display = 'block';
            visibleCount++;
        } else {
            code.style.display = 'none';
        }
    });
    
    noResults.style.display = visibleCount === 0 ? 'block' : 'none';
});
</script>

<%- include('partials/footer') %>
