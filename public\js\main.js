// Main JavaScript for Penetration Testing Learning Platform

// Theme Management
class ThemeManager {
    constructor() {
        this.theme = localStorage.getItem('theme') || 'dark';
        this.init();
    }
    
    init() {
        this.applyTheme();
        this.setupToggle();
    }
    
    applyTheme() {
        document.documentElement.setAttribute('data-theme', this.theme);
        document.body.className = this.theme === 'dark' ? 'bg-gray-900 text-gray-100' : 'bg-gray-50 text-gray-900';

        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            const icon = themeToggle.querySelector('i');
            if (this.theme === 'dark') {
                icon.className = 'fas fa-sun';
                themeToggle.setAttribute('title', 'Switch to light mode');
            } else {
                icon.className = 'fas fa-moon';
                themeToggle.setAttribute('title', 'Switch to dark mode');
            }
        }

        // Update theme-aware elements
        this.updateThemeElements();
    }

    updateThemeElements() {
        const elements = document.querySelectorAll('[class*="bg-gray"], [class*="text-gray"]');
        elements.forEach(element => {
            if (this.theme === 'light') {
                element.classList.forEach(className => {
                    if (className.includes('bg-gray-900')) {
                        element.classList.replace(className, 'bg-white');
                    } else if (className.includes('bg-gray-800')) {
                        element.classList.replace(className, 'bg-gray-50');
                    } else if (className.includes('text-gray-100')) {
                        element.classList.replace(className, 'text-gray-900');
                    }
                });
            }
        });
    }
    
    setupToggle() {
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggle());
        }
    }
    
    toggle() {
        this.theme = this.theme === 'dark' ? 'light' : 'dark';
        localStorage.setItem('theme', this.theme);
        this.applyTheme();
    }
}

// Progress Tracking
class ProgressTracker {
    constructor() {
        this.completedSlides = JSON.parse(localStorage.getItem('completedSlides') || '[]');
        this.completedTools = JSON.parse(localStorage.getItem('completedTools') || '[]');
        this.completedExamples = JSON.parse(localStorage.getItem('completedExamples') || '[]');
    }
    
    markSlideComplete(slideId) {
        if (!this.completedSlides.includes(slideId)) {
            this.completedSlides.push(slideId);
            this.save();
        }
    }
    
    markSlideIncomplete(slideId) {
        const index = this.completedSlides.indexOf(slideId);
        if (index > -1) {
            this.completedSlides.splice(index, 1);
            this.save();
        }
    }
    
    isSlideComplete(slideId) {
        return this.completedSlides.includes(slideId);
    }
    
    getProgress() {
        return {
            slides: this.completedSlides.length,
            tools: this.completedTools.length,
            examples: this.completedExamples.length
        };
    }
    
    save() {
        localStorage.setItem('completedSlides', JSON.stringify(this.completedSlides));
        localStorage.setItem('completedTools', JSON.stringify(this.completedTools));
        localStorage.setItem('completedExamples', JSON.stringify(this.completedExamples));
    }
}

// Search Functionality
class SearchManager {
    constructor() {
        this.searchInput = document.getElementById('search-input');
        this.searchResults = document.getElementById('search-results');
        this.debounceTimer = null;
        this.init();
    }
    
    init() {
        if (this.searchInput) {
            this.searchInput.addEventListener('input', (e) => this.handleSearch(e));
            document.addEventListener('click', (e) => this.handleClickOutside(e));
        }
    }
    
    handleSearch(e) {
        clearTimeout(this.debounceTimer);
        const query = e.target.value.trim();
        
        if (query.length < 2) {
            this.hideResults();
            return;
        }
        
        this.debounceTimer = setTimeout(() => {
            this.performSearch(query);
        }, 300);
    }
    
    async performSearch(query) {
        try {
            const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
            const results = await response.json();
            this.displayResults(results);
        } catch (error) {
            console.error('Search error:', error);
            this.displayError();
        }
    }
    
    displayResults(results) {
        if (!this.searchResults) return;
        
        if (results.length === 0) {
            this.searchResults.innerHTML = '<div class="p-3 text-gray-400">No results found</div>';
        } else {
            this.searchResults.innerHTML = results.map(result => `
                <a href="${result.url}" class="block p-3 hover:bg-gray-600 border-b border-gray-600 last:border-b-0 transition-colors">
                    <div class="font-medium text-white">${this.highlightQuery(result.title, this.searchInput.value)}</div>
                    <div class="text-sm text-gray-400">${result.type} • ${result.excerpt}</div>
                </a>
            `).join('');
        }
        
        this.showResults();
    }
    
    displayError() {
        if (this.searchResults) {
            this.searchResults.innerHTML = '<div class="p-3 text-red-400">Search error occurred</div>';
            this.showResults();
        }
    }
    
    highlightQuery(text, query) {
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<span class="search-highlight">$1</span>');
    }
    
    showResults() {
        if (this.searchResults) {
            this.searchResults.classList.remove('hidden');
        }
    }
    
    hideResults() {
        if (this.searchResults) {
            this.searchResults.classList.add('hidden');
        }
    }
    
    handleClickOutside(e) {
        if (this.searchInput && this.searchResults && 
            !this.searchInput.contains(e.target) && 
            !this.searchResults.contains(e.target)) {
            this.hideResults();
        }
    }
}

// Code Syntax Highlighting
class CodeHighlighter {
    constructor() {
        this.init();
    }
    
    init() {
        // Initialize Prism.js for code highlighting
        if (typeof Prism !== 'undefined') {
            Prism.highlightAll();
        }
        
        // Add copy buttons to code blocks
        this.addCopyButtons();
    }
    
    addCopyButtons() {
        const codeBlocks = document.querySelectorAll('pre[class*="language-"]');
        
        codeBlocks.forEach(block => {
            const button = document.createElement('button');
            button.className = 'absolute top-2 right-2 bg-gray-600 hover:bg-gray-500 text-white px-2 py-1 rounded text-xs transition-colors';
            button.innerHTML = '<i class="fas fa-copy mr-1"></i>Copy';
            
            const container = document.createElement('div');
            container.className = 'relative';
            block.parentNode.insertBefore(container, block);
            container.appendChild(block);
            container.appendChild(button);
            
            button.addEventListener('click', () => this.copyCode(block, button));
        });
    }
    
    async copyCode(block, button) {
        const code = block.querySelector('code').textContent;
        
        try {
            await navigator.clipboard.writeText(code);
            button.innerHTML = '<i class="fas fa-check mr-1"></i>Copied!';
            button.classList.add('bg-green-600');
            
            setTimeout(() => {
                button.innerHTML = '<i class="fas fa-copy mr-1"></i>Copy';
                button.classList.remove('bg-green-600');
            }, 2000);
        } catch (error) {
            console.error('Copy failed:', error);
            button.innerHTML = '<i class="fas fa-times mr-1"></i>Failed';
            button.classList.add('bg-red-600');
            
            setTimeout(() => {
                button.innerHTML = '<i class="fas fa-copy mr-1"></i>Copy';
                button.classList.remove('bg-red-600');
            }, 2000);
        }
    }
}

// Slide Navigation
class SlideNavigator {
    constructor() {
        this.currentSlide = 1;
        this.totalSlides = 35;
        this.init();
    }
    
    init() {
        this.createNavigationControls();
        this.setupKeyboardNavigation();
    }
    
    createNavigationControls() {
        const nav = document.createElement('div');
        nav.className = 'slide-nav no-print';
        nav.innerHTML = `
            <button id="prev-slide" title="Previous Slide">
                <i class="fas fa-chevron-left"></i>
            </button>
            <button id="next-slide" title="Next Slide">
                <i class="fas fa-chevron-right"></i>
            </button>
            <button id="slide-overview" title="Slide Overview">
                <i class="fas fa-th"></i>
            </button>
        `;
        
        document.body.appendChild(nav);
        
        document.getElementById('prev-slide')?.addEventListener('click', () => this.previousSlide());
        document.getElementById('next-slide')?.addEventListener('click', () => this.nextSlide());
        document.getElementById('slide-overview')?.addEventListener('click', () => this.showOverview());
    }
    
    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;
            
            switch(e.key) {
                case 'ArrowLeft':
                    e.preventDefault();
                    this.previousSlide();
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    this.nextSlide();
                    break;
                case 'Escape':
                    this.showOverview();
                    break;
            }
        });
    }
    
    previousSlide() {
        if (this.currentSlide > 1) {
            window.location.href = `/slides/${this.currentSlide - 1}`;
        }
    }
    
    nextSlide() {
        if (this.currentSlide < this.totalSlides) {
            window.location.href = `/slides/${this.currentSlide + 1}`;
        }
    }
    
    showOverview() {
        window.location.href = '/slides';
    }
}

// Notification System
class NotificationManager {
    constructor() {
        this.container = this.createContainer();
    }
    
    createContainer() {
        const container = document.createElement('div');
        container.id = 'notification-container';
        container.className = 'fixed top-4 right-4 z-50 space-y-2';
        document.body.appendChild(container);
        return container;
    }
    
    show(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `notification bg-gray-800 border-l-4 p-4 rounded shadow-lg max-w-sm transform translate-x-full transition-transform duration-300`;
        
        const colors = {
            success: 'border-green-500 text-green-100',
            error: 'border-red-500 text-red-100',
            warning: 'border-yellow-500 text-yellow-100',
            info: 'border-blue-500 text-blue-100'
        };
        
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        
        notification.classList.add(...colors[type].split(' '));
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="${icons[type]} mr-2"></i>
                <span>${message}</span>
                <button class="ml-auto text-gray-400 hover:text-white" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        this.container.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);
        
        // Auto remove
        if (duration > 0) {
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => notification.remove(), 300);
            }, duration);
        }
    }
}

// Enhanced Animation Manager
class AnimationManager {
    constructor() {
        this.observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        this.init();
    }

    init() {
        this.setupScrollAnimations();
        this.setupHoverEffects();
        this.setupLoadingAnimations();
    }

    setupScrollAnimations() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;
                    const animationType = element.dataset.animate || 'fade-in';
                    element.classList.add(`animate-${animationType}`);
                    observer.unobserve(element);
                }
            });
        }, this.observerOptions);

        // Observe elements with animation data attributes
        document.querySelectorAll('[data-animate]').forEach(el => {
            observer.observe(el);
        });

        // Auto-animate cards and sections
        document.querySelectorAll('.card-enhanced, .tool-card, .status-code-card, .timeline-item').forEach((el, index) => {
            el.dataset.animate = index % 2 === 0 ? 'slide-in-left' : 'slide-in-right';
            observer.observe(el);
        });
    }

    setupHoverEffects() {
        // Add hover lift effect to interactive elements
        document.querySelectorAll('.tool-card, .status-code-card, .slide-card').forEach(card => {
            card.classList.add('hover-lift');
        });

        // Add glow effect to primary buttons
        document.querySelectorAll('.btn-primary, .bg-red-600').forEach(btn => {
            btn.classList.add('hover-glow');
        });
    }

    setupLoadingAnimations() {
        // Stagger animation for grid items
        document.querySelectorAll('.grid > *').forEach((item, index) => {
            item.style.animationDelay = `${index * 0.1}s`;
        });
    }

    triggerAnimation(element, animationType) {
        element.classList.add(`animate-${animationType}`);
        element.addEventListener('animationend', () => {
            element.classList.remove(`animate-${animationType}`);
        }, { once: true });
    }
}

// Enhanced Performance Manager
class PerformanceManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupLazyLoading();
        this.setupImageOptimization();
        this.setupCaching();
    }

    setupLazyLoading() {
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('loading');
                    imageObserver.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            img.classList.add('loading');
            imageObserver.observe(img);
        });
    }

    setupImageOptimization() {
        // Add loading states to images
        document.querySelectorAll('img').forEach(img => {
            img.addEventListener('load', () => {
                img.classList.add('loaded');
            });

            img.addEventListener('error', () => {
                img.classList.add('error');
                img.src = '/images/placeholder.svg';
            });
        });
    }

    setupCaching() {
        // Cache frequently accessed data
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js').catch(console.error);
        }
    }
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize managers
    const themeManager = new ThemeManager();
    const progressTracker = new ProgressTracker();
    const searchManager = new SearchManager();
    const codeHighlighter = new CodeHighlighter();
    const slideNavigator = new SlideNavigator();
    const notificationManager = new NotificationManager();
    const animationManager = new AnimationManager();
    const performanceManager = new PerformanceManager();

    // Make managers globally available
    window.themeManager = themeManager;
    window.progressTracker = progressTracker;
    window.notificationManager = notificationManager;
    window.animationManager = animationManager;
    
    // Add smooth scrolling to anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Add loading states to buttons
    document.querySelectorAll('button[type="submit"], .btn-loading').forEach(button => {
        button.addEventListener('click', function() {
            if (!this.disabled) {
                this.classList.add('loading');
                this.disabled = true;
                
                setTimeout(() => {
                    this.classList.remove('loading');
                    this.disabled = false;
                }, 2000);
            }
        });
    });
    
    // Initialize tooltips
    const tooltips = document.querySelectorAll('[data-tooltip]');
    tooltips.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
    
    function showTooltip(e) {
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip absolute bg-gray-800 text-white px-2 py-1 rounded text-sm z-50';
        tooltip.textContent = e.target.getAttribute('data-tooltip');
        document.body.appendChild(tooltip);
        
        const rect = e.target.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
    }
    
    function hideTooltip() {
        const tooltip = document.querySelector('.tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    }
});
