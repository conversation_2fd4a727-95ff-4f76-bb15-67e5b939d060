<%- include('partials/header') %>

<!-- Methodology Header -->
<section class="bg-gradient-to-r from-gray-900 via-purple-900 to-gray-900 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl font-bold text-white mb-4">
                <i class="fas fa-list-ol mr-3"></i>
                Penetration Testing Methodology
            </h1>
            <p class="text-xl text-gray-300 mb-6">
                Systematic approach to penetration testing following industry standards
            </p>
            <div class="flex justify-center space-x-4 flex-wrap">
                <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm mb-2">PTES</span>
                <span class="bg-green-600 text-white px-3 py-1 rounded-full text-sm mb-2">OWASP</span>
                <span class="bg-red-600 text-white px-3 py-1 rounded-full text-sm mb-2">NIST</span>
                <span class="bg-purple-600 text-white px-3 py-1 rounded-full text-sm mb-2">OSSTMM</span>
            </div>
        </div>
    </div>
</section>

<!-- Methodology Overview -->
<section class="py-12 bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-white mb-4">7-Phase Methodology</h2>
            <p class="text-gray-400 max-w-3xl mx-auto">
                Our comprehensive methodology follows industry best practices and standards, 
                ensuring thorough and systematic security testing.
            </p>
        </div>
        
        <!-- Timeline -->
        <div class="timeline relative">
            <% methodology.phases.forEach((phase, index) => { %>
            <div class="timeline-item mb-12" data-phase="<%= index + 1 %>">
                <div class="bg-gray-800 rounded-lg p-6 hover:bg-gray-700 transition-colors">
                    <!-- Phase Header -->
                    <div class="flex items-center mb-4">
                        <div class="bg-purple-600 text-white rounded-full w-10 h-10 flex items-center justify-center font-bold mr-4">
                            <%= index + 1 %>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold text-white"><%= phase.name %></h3>
                            <p class="text-gray-400 text-sm"><%= phase.description %></p>
                        </div>
                    </div>
                    
                    <!-- Phase Content -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Activities -->
                        <div>
                            <h4 class="text-lg font-semibold text-purple-400 mb-3">
                                <i class="fas fa-tasks mr-2"></i>
                                Key Activities
                            </h4>
                            <ul class="space-y-2">
                                <% phase.activities.forEach(activity => { %>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-green-400 mr-2 mt-1 flex-shrink-0"></i>
                                    <span class="text-gray-300 text-sm"><%= activity %></span>
                                </li>
                                <% }); %>
                            </ul>
                        </div>
                        
                        <!-- Deliverables -->
                        <div>
                            <h4 class="text-lg font-semibold text-blue-400 mb-3">
                                <i class="fas fa-file-alt mr-2"></i>
                                Deliverables
                            </h4>
                            <ul class="space-y-2">
                                <% phase.deliverables.forEach(deliverable => { %>
                                <li class="flex items-start">
                                    <i class="fas fa-document text-blue-400 mr-2 mt-1 flex-shrink-0"></i>
                                    <span class="text-gray-300 text-sm"><%= deliverable %></span>
                                </li>
                                <% }); %>
                            </ul>
                        </div>
                        
                        <!-- Tools -->
                        <div>
                            <h4 class="text-lg font-semibold text-yellow-400 mb-3">
                                <i class="fas fa-tools mr-2"></i>
                                Common Tools
                            </h4>
                            <div class="flex flex-wrap gap-2">
                                <% phase.tools.forEach(tool => { %>
                                <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded"><%= tool %></span>
                                <% }); %>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Expand/Collapse Button -->
                    <button onclick="togglePhaseDetails(<%= index + 1 %>)" 
                            class="mt-4 text-purple-400 hover:text-purple-300 text-sm font-semibold">
                        <i class="fas fa-chevron-down mr-1"></i>
                        Show detailed steps
                    </button>
                    
                    <!-- Detailed Steps (Initially Hidden) -->
                    <div id="phase-details-<%= index + 1 %>" class="hidden mt-4 pt-4 border-t border-gray-700">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h5 class="text-md font-semibold text-white mb-2">Detailed Steps:</h5>
                                <ol class="list-decimal list-inside space-y-1 text-sm text-gray-300">
                                    <% phase.activities.forEach(activity => { %>
                                    <li><%= activity %></li>
                                    <% }); %>
                                </ol>
                            </div>
                            <div>
                                <h5 class="text-md font-semibold text-white mb-2">Best Practices:</h5>
                                <ul class="space-y-1 text-sm text-gray-300">
                                    <li class="flex items-start">
                                        <i class="fas fa-lightbulb text-yellow-400 mr-2 mt-1 flex-shrink-0"></i>
                                        Document all findings thoroughly
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-lightbulb text-yellow-400 mr-2 mt-1 flex-shrink-0"></i>
                                        Follow legal and ethical guidelines
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-lightbulb text-yellow-400 mr-2 mt-1 flex-shrink-0"></i>
                                        Maintain clear communication with stakeholders
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <% }); %>
        </div>
    </div>
</section>

<!-- Standards and Frameworks -->
<section class="py-12 bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-white mb-4">Industry Standards</h2>
            <p class="text-gray-400">Recognized frameworks and methodologies</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <% methodology.standards.forEach(standard => { %>
            <div class="bg-gray-700 rounded-lg p-6 hover:bg-gray-600 transition-colors">
                <h3 class="text-xl font-semibold text-white mb-3"><%= standard.name %></h3>
                <p class="text-gray-300 text-sm mb-4"><%= standard.description %></p>
                <a href="<%= standard.url %>" target="_blank" 
                   class="text-purple-400 hover:text-purple-300 text-sm font-semibold">
                    Learn More <i class="fas fa-external-link-alt ml-1"></i>
                </a>
            </div>
            <% }); %>
        </div>
    </div>
</section>

<!-- OWASP Top 10 Section -->
<section class="py-12 bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-white mb-4">OWASP Top 10 - 2021</h2>
            <p class="text-gray-400">Most critical web application security risks</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div class="bg-red-600 rounded-lg p-4 text-center hover:bg-red-700 transition-colors">
                <div class="text-2xl font-bold text-white mb-2">A01</div>
                <div class="text-sm text-red-100">Broken Access Control</div>
            </div>
            <div class="bg-red-600 rounded-lg p-4 text-center hover:bg-red-700 transition-colors">
                <div class="text-2xl font-bold text-white mb-2">A02</div>
                <div class="text-sm text-red-100">Cryptographic Failures</div>
            </div>
            <div class="bg-red-600 rounded-lg p-4 text-center hover:bg-red-700 transition-colors">
                <div class="text-2xl font-bold text-white mb-2">A03</div>
                <div class="text-sm text-red-100">Injection</div>
            </div>
            <div class="bg-red-600 rounded-lg p-4 text-center hover:bg-red-700 transition-colors">
                <div class="text-2xl font-bold text-white mb-2">A04</div>
                <div class="text-sm text-red-100">Insecure Design</div>
            </div>
            <div class="bg-red-600 rounded-lg p-4 text-center hover:bg-red-700 transition-colors">
                <div class="text-2xl font-bold text-white mb-2">A05</div>
                <div class="text-sm text-red-100">Security Misconfiguration</div>
            </div>
            <div class="bg-orange-600 rounded-lg p-4 text-center hover:bg-orange-700 transition-colors">
                <div class="text-2xl font-bold text-white mb-2">A06</div>
                <div class="text-sm text-orange-100">Vulnerable Components</div>
            </div>
            <div class="bg-orange-600 rounded-lg p-4 text-center hover:bg-orange-700 transition-colors">
                <div class="text-2xl font-bold text-white mb-2">A07</div>
                <div class="text-sm text-orange-100">ID & Auth Failures</div>
            </div>
            <div class="bg-orange-600 rounded-lg p-4 text-center hover:bg-orange-700 transition-colors">
                <div class="text-2xl font-bold text-white mb-2">A08</div>
                <div class="text-sm text-orange-100">Software Integrity</div>
            </div>
            <div class="bg-yellow-600 rounded-lg p-4 text-center hover:bg-yellow-700 transition-colors">
                <div class="text-2xl font-bold text-white mb-2">A09</div>
                <div class="text-sm text-yellow-100">Logging Failures</div>
            </div>
            <div class="bg-yellow-600 rounded-lg p-4 text-center hover:bg-yellow-700 transition-colors">
                <div class="text-2xl font-bold text-white mb-2">A10</div>
                <div class="text-sm text-yellow-100">Server-Side Request Forgery</div>
            </div>
        </div>
    </div>
</section>

<!-- Quick Actions -->
<section class="py-8 bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div class="text-center md:text-left">
                <h3 class="text-lg font-semibold text-white mb-2">Ready to start testing?</h3>
                <p class="text-gray-400">Explore our tools and examples to begin your penetration testing journey</p>
            </div>
            <div class="flex space-x-4">
                <a href="/tools" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold transition-colors">
                    <i class="fas fa-tools mr-2"></i>
                    View Tools
                </a>
                <a href="/examples" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-semibold transition-colors">
                    <i class="fas fa-code mr-2"></i>
                    Code Examples
                </a>
            </div>
        </div>
    </div>
</section>

<script>
function togglePhaseDetails(phaseNumber) {
    const details = document.getElementById(`phase-details-${phaseNumber}`);
    const button = document.querySelector(`[data-phase="${phaseNumber}"] button`);
    const icon = button.querySelector('i');
    
    if (details.classList.contains('hidden')) {
        details.classList.remove('hidden');
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-up');
        button.innerHTML = button.innerHTML.replace('Show detailed steps', 'Hide detailed steps');
    } else {
        details.classList.add('hidden');
        icon.classList.remove('fa-chevron-up');
        icon.classList.add('fa-chevron-down');
        button.innerHTML = button.innerHTML.replace('Hide detailed steps', 'Show detailed steps');
    }
}

// Add smooth scrolling for timeline navigation
document.addEventListener('DOMContentLoaded', function() {
    const timelineItems = document.querySelectorAll('.timeline-item');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
            }
        });
    }, { threshold: 0.1 });
    
    timelineItems.forEach(item => {
        observer.observe(item);
    });
});
</script>

<%- include('partials/footer') %>
