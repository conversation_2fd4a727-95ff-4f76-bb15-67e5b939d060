<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - PenTest Academy</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1f2937, #111827);
            color: #f9fafb;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            text-align: center;
            max-width: 500px;
            padding: 2rem;
        }
        
        .icon {
            font-size: 4rem;
            color: #ef4444;
            margin-bottom: 1rem;
        }
        
        h1 {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #f9fafb;
        }
        
        p {
            color: #d1d5db;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .btn {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(239, 68, 68, 0.3);
        }
        
        .features {
            margin-top: 2rem;
            text-align: left;
        }
        
        .feature {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            color: #9ca3af;
        }
        
        .feature-icon {
            color: #10b981;
            margin-right: 0.5rem;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon pulse">📡</div>
        <h1>You're Offline</h1>
        <p>
            It looks like you're not connected to the internet. Don't worry - you can still access 
            some cached content from PenTest Academy while offline.
        </p>
        
        <button class="btn" onclick="location.reload()">Try Again</button>
        
        <div class="features">
            <h3 style="color: #f9fafb; margin-bottom: 1rem;">Available Offline:</h3>
            <div class="feature">
                <span class="feature-icon">✓</span>
                Previously viewed slides
            </div>
            <div class="feature">
                <span class="feature-icon">✓</span>
                Cached tool references
            </div>
            <div class="feature">
                <span class="feature-icon">✓</span>
                Status code documentation
            </div>
            <div class="feature">
                <span class="feature-icon">✓</span>
                Code examples
            </div>
        </div>
        
        <p style="margin-top: 2rem; font-size: 0.875rem;">
            Your progress and bookmarks are saved locally and will sync when you're back online.
        </p>
    </div>
    
    <script>
        // Check for connection and auto-reload
        window.addEventListener('online', () => {
            location.reload();
        });
        
        // Show connection status
        function updateConnectionStatus() {
            if (navigator.onLine) {
                location.reload();
            }
        }
        
        // Check connection every 5 seconds
        setInterval(updateConnectionStatus, 5000);
    </script>
</body>
</html>
