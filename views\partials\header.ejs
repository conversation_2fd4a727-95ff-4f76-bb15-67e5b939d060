<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <meta name="description" content="Comprehensive Penetration Testing Learning Platform - From Basic to Advanced">
    <meta name="keywords" content="penetration testing, ethical hacking, cybersecurity, security testing, OWASP">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/images/favicon.ico">
    
    <!-- CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <link href="/css/style.css" rel="stylesheet">
    
    <!-- JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/alpinejs/3.10.2/cdn.min.js" defer></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</head>
<body class="bg-gray-900 text-gray-100 min-h-screen">
    <!-- Navigation -->
    <nav class="nav-enhanced bg-gray-800 shadow-lg sticky top-0 z-50" x-data="{ mobileMenuOpen: false, searchOpen: false }" data-animate="slide-in-top">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <!-- Logo and Brand -->
                <div class="flex items-center">
                    <a href="/" class="flex items-center space-x-2">
                        <i class="fas fa-shield-alt text-red-500 text-2xl"></i>
                        <span class="text-xl font-bold text-white">PenTest Academy</span>
                    </a>
                </div>
                
                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="/" class="nav-item text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors">
                        <i class="fas fa-home mr-1"></i>Home
                    </a>
                    <a href="/slides" class="nav-item text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors">
                        <i class="fas fa-presentation mr-1"></i>Slides
                    </a>
                    <a href="/methodology" class="nav-item text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors">
                        <i class="fas fa-list-ol mr-1"></i>Methodology
                    </a>
                    <a href="/tools" class="nav-item text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors">
                        <i class="fas fa-tools mr-1"></i>Tools
                    </a>
                    <a href="/status-codes" class="nav-item text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors">
                        <i class="fas fa-code mr-1"></i>Status Codes
                    </a>
                    <a href="/examples" class="nav-item text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors">
                        <i class="fas fa-code-branch mr-1"></i>Examples
                    </a>
                    <a href="/advanced" class="nav-item text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors">
                        <i class="fas fa-graduation-cap mr-1"></i>Advanced
                    </a>
                </div>
                
                <!-- Search and Mobile Menu Button -->
                <div class="flex items-center space-x-4">
                    <!-- Search Button -->
                    <button @click="searchOpen = !searchOpen" class="text-gray-300 hover:text-white p-2 rounded-md transition-colors">
                        <i class="fas fa-search"></i>
                    </button>
                    
                    <!-- Theme Toggle -->
                    <button id="theme-toggle" class="dark-mode-toggle text-gray-300 hover:text-white p-2 rounded-md transition-colors" title="Switch to light mode">
                        <i class="fas fa-moon"></i>
                    </button>
                    
                    <!-- Mobile Menu Button -->
                    <button @click="mobileMenuOpen = !mobileMenuOpen" class="md:hidden text-gray-300 hover:text-white p-2 rounded-md transition-colors">
                        <i class="fas fa-bars" x-show="!mobileMenuOpen"></i>
                        <i class="fas fa-times" x-show="mobileMenuOpen"></i>
                    </button>
                </div>
            </div>
            
            <!-- Search Bar -->
            <div x-show="searchOpen" x-transition class="pb-4">
                <div class="relative">
                    <input type="text" id="search-input" placeholder="Search slides, tools, techniques..." 
                           class="w-full bg-gray-700 text-white placeholder-gray-400 rounded-lg px-4 py-2 pl-10 focus:outline-none focus:ring-2 focus:ring-red-500">
                    <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                </div>
                <div id="search-results" class="mt-2 bg-gray-700 rounded-lg shadow-lg hidden"></div>
            </div>
            
            <!-- Mobile Navigation -->
            <div x-show="mobileMenuOpen" x-transition class="md:hidden pb-4">
                <div class="space-y-1">
                    <a href="/" class="block text-gray-300 hover:text-white px-3 py-2 rounded-md text-base font-medium">
                        <i class="fas fa-home mr-2"></i>Home
                    </a>
                    <a href="/slides" class="block text-gray-300 hover:text-white px-3 py-2 rounded-md text-base font-medium">
                        <i class="fas fa-presentation mr-2"></i>Slides
                    </a>
                    <a href="/methodology" class="block text-gray-300 hover:text-white px-3 py-2 rounded-md text-base font-medium">
                        <i class="fas fa-list-ol mr-2"></i>Methodology
                    </a>
                    <a href="/tools" class="block text-gray-300 hover:text-white px-3 py-2 rounded-md text-base font-medium">
                        <i class="fas fa-tools mr-2"></i>Tools
                    </a>
                    <a href="/status-codes" class="block text-gray-300 hover:text-white px-3 py-2 rounded-md text-base font-medium">
                        <i class="fas fa-code mr-2"></i>Status Codes
                    </a>
                    <a href="/examples" class="block text-gray-300 hover:text-white px-3 py-2 rounded-md text-base font-medium">
                        <i class="fas fa-code-branch mr-2"></i>Examples
                    </a>
                    <a href="/advanced" class="block text-gray-300 hover:text-white px-3 py-2 rounded-md text-base font-medium">
                        <i class="fas fa-graduation-cap mr-2"></i>Advanced
                    </a>
                </div>
            </div>
        </div>
    </nav>
